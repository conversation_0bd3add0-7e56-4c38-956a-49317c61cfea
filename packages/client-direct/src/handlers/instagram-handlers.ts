import { Request, Response } from "express";
import {
    <PERSON><PERSON><PERSON><PERSON>,
    el<PERSON><PERSON><PERSON>ger,
    stringToUuid,
    instagramCharacter,
} from "@elizaos/core";
import { InstagramClientInterface } from "agent-instagram-client";
import { IDirectClient } from "../models/types";

export const handleStartInstagramAgent = async (
    req: Request,
    res: Response,
    directClient: IDirectClient
): Promise<Response> => {
    const { projectName, projectId } = req.body;
    const agentId = stringToUuid("instagram-agent-" + projectName);

    // Create runtime with default character configuration
    const runtime: AgentRuntime = await directClient.startInstagramAgent({
        ...instagramCharacter,
        name: projectName,
        id: agentId,
        username: projectName,
    });

    const rooms = await directClient.db.getRooms();
    const room = rooms.find((r: any) => r.id === runtime.agentId);
    const roomSettings = room ? JSON.parse(room.settings) : {};

    directClient.agents.set(runtime.agentId, runtime);

    try {
        await InstagramClientInterface.stop(runtime);
        const { url, manager: instagramClient } =
            await InstagramClientInterface.start(projectId, runtime);

        if (instagramClient && url) {
            if (roomSettings && room && room.status === "active") {
                // This should not be a case, but handle if needed
                runtime.clients.instagram = instagramClient;
                return res.status(200).json({ success: true });
            } else {
                runtime.clients.instagram = instagramClient;
                return res.status(200).json({ success: true, url });
            }
        }

        directClient.unregisterAgent(directClient.agents.get(agentId));
        return res.status(403).json({
            success: false,
            message:
                "Failed to start Instagram client. Check your credentials.",
        });
    } catch (error) {
        console.log(error);
        directClient.unregisterAgent(directClient.agents.get(agentId));
        return res.status(403).json({
            success: false,
            message:
                "Failed to start Instagram client. Check your credentials.",
        });
    }
};

export const handleInstagramCallback = async (
    req: Request,
    res: Response,
    directClient: IDirectClient
) => {
    const code = req.query.code as string;
    const state = req.query.state as string;
    const [agentId, projectId] = state.split(":");
    const runtime: AgentRuntime = directClient.agents.get(agentId);
    const instagramClient = runtime.clients.instagram;

    try {
        const tokenResponse = await fetch(
            `https://api.instagram.com/oauth/access_token`,
            {
                method: "POST",
                headers: {
                    "Content-Type": "application/x-www-form-urlencoded",
                },
                body: new URLSearchParams({
                    client_id: process.env.INSTAGRAM_CLIENT_ID!,
                    client_secret: process.env.INSTAGRAM_CLIENT_SECRET!,
                    grant_type: "authorization_code",
                    redirect_uri: process.env.INSTAGRAM_REDIRECT_URI!,
                    code,
                }).toString(),
            }
        );

        if (!tokenResponse.ok) {
            const errorText = await tokenResponse.text();
            elizaLogger.error(
                `Failed to exchange auth code: ${tokenResponse.statusText} - ${errorText}`
            );
            return res.redirect(
                `${process.env.INSTAGRAM_FRONTEND_URL}/api/auth/callback/instagram?projectId=${projectId}&error=token_exchange_failed`
            );
        }

        const tokenData = await tokenResponse.json();

        // Update runtime character settings
        runtime.character = {
            ...runtime.character,
            settings: {
                secrets: {
                    INSTAGRAM_ACCESS_TOKEN: tokenData.access_token,
                    INSTAGRAM_EXPIRES_IN: (
                        Date.now() +
                        (tokenData.expires_in || 0) * 1000
                    ).toString(),
                },
            },
        };

        if (!runtime || !runtime.clients || !runtime.clients.instagram) {
            elizaLogger.error(
                `Agent runtime or Instagram client not found for agent ID: ${agentId}`
            );
            return res.redirect(
                `${process.env.INSTAGRAM_FRONTEND_URL}/api/auth/callback/instagram?projectId=${projectId}&error=agent_not_found`
            );
        }

        // Update client credentials
        instagramClient.auth.credentials = {
            accessToken: tokenData.access_token,
            expiresAt: Date.now() + (tokenData.expires_in || 0) * 1000,
        };

        try {
            await instagramClient.api.getProfile();
            // Optionally, start post scheduling or other features here
            await directClient.db.updateRoomStatus(
                runtime.agentId,
                "active",
                JSON.stringify({ ...runtime.character }),
                JSON.stringify({
                    schedulingPosts: false,
                    postInterval: 0,
                })
            );
        } catch (error) {
            elizaLogger.error("Instagram authentication error:", error);
            return res.redirect(
                `${process.env.INSTAGRAM_FRONTEND_URL}/api/auth/callback/instagram?projectId=${projectId}&error=authentication_failed`
            );
        }
        return res.redirect(
            `${process.env.INSTAGRAM_FRONTEND_URL}/api/auth/callback/instagram?projectId=${projectId}&success=true&agentId=${agentId}&username=${runtime.character.username}`
        );
    } catch (error) {
        elizaLogger.error("Instagram authentication error:", error);
        return res.redirect(
            `${process.env.INSTAGRAM_FRONTEND_URL}/api/auth/callback/instagram?projectId=${projectId}&error=authentication_failed`
        );
    }
};
