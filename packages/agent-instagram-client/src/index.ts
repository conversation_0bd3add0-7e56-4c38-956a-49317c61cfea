import { elizaLogger, IAgentRuntime } from "@elizaos/core";
import { InstagramAuth } from "./instagramAuth";
import { InstagramAPI } from "./instagramApi";
import { ClientBase } from "./base";
import { InstagramPostClient } from "./instagramPost";

class InstagramManager {
    auth: InstagramAuth;
    api: InstagramAPI;
    client: ClientBase;
    post: InstagramPostClient;
    constructor(runtime: IAgentRuntime) {
        this.auth = new InstagramAuth();
        this.api = new InstagramAPI(this.auth);
        this.client = new ClientBase(runtime, this.auth);
        this.post = new InstagramPostClient(this.client, runtime);
    }
}

export const InstagramClientInterface = {
    async start(ideaId: string, runtime: IAgentRuntime) {
        elizaLogger.log("Instagram auth url generating");
        const manager = new InstagramManager(runtime);
        if (manager.client.profile.id) {
            await manager.post.start();
        }
        return {
            manager,
            url: manager.auth.generateAuthUrl(runtime.agentId, ideaId),
        };
    },
    async stop(_runtime: IAgentRuntime) {
        const instagramClient = _runtime.clients.instagram as InstagramManager;
        if (instagramClient) {
            elizaLogger.log("Instagram client trying to stop");
            instagramClient.post.stopNewPosts();
            return instagramClient;
        }
    },
};

export default InstagramClientInterface;
