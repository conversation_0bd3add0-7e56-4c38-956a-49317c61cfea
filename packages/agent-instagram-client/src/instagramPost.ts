import { ClientBase } from "./base";
import { IAgentRuntime } from "@elizaos/core";

export class InstagramPostClient {
    client: ClientBase;
    runtime: IAgentRuntime;

    constructor(client: ClientBase, runtime: IAgentRuntime) {
        this.client = client;
        this.runtime = runtime;
    }

    async start() {
        // Start posting logic for Instagram
        // ... implement ...
    }

    stopNewPosts() {
        // Stop posting logic
        // ... implement ...
    }
}
