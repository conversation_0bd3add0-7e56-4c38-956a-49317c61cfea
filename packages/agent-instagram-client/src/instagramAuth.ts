import { IAgentRuntime } from "@elizaos/core";

export interface InstagramCredentials {
    accessToken?: string;
    refreshToken?: string;
    expiresAt?: number;
    refreshTokenExpiresAt?: number;
    username?: string;
}

export class InstagramAuth {
    credentials: InstagramCredentials = {
        accessToken: "",
        refreshToken: "",
        expiresAt: 0,
        refreshTokenExpiresAt: 0,
        username: "",
    };
    accessToken: string | null = null;
    refreshToken: string | null = null;
    expiresIn: number | null = null;

    constructor() {
        // ... any initialization ...
    }

    generateAuthUrl(agentId: string, ideaId: string): string {
        // Instagram OAuth endpoint and parameters
        const clientId = process.env.INSTAGRAM_CLIENT_ID;
        const redirectUri = encodeURIComponent(
            process.env.INSTAGRAM_REDIRECT_URI!
        );
        const scope =
            "user_profile,user_media,instagram_business_content_publish,instagram_business_basic,instagram_business_manage_comments,instagram_business_manage_messages";
        const state = `${agentId}:${ideaId}`;
        return `https://www.instagram.com/oauth/authorize?client_id=${clientId}&redirect_uri=${redirectUri}&scope=${scope}&response_type=code&state=${state}`;
    }

    async fetchAccessToken(code: string): Promise<void> {
        const clientId = process.env.INSTAGRAM_CLIENT_ID!;
        const clientSecret = process.env.INSTAGRAM_CLIENT_SECRET!;
        const redirectUri = process.env.INSTAGRAM_REDIRECT_URI!;

        // Step 1: Exchange code for short-lived access token
        const tokenRes = await fetch(
            "https://api.instagram.com/oauth/access_token",
            {
                method: "POST",
                headers: {
                    "Content-Type": "application/x-www-form-urlencoded",
                },
                body: new URLSearchParams({
                    client_id: clientId,
                    client_secret: clientSecret,
                    grant_type: "authorization_code",
                    redirect_uri: redirectUri,
                    code,
                }),
            }
        );

        if (!tokenRes.ok) {
            throw new Error(
                `Failed to fetch Instagram access token: ${tokenRes.statusText}`
            );
        }

        const tokenData = await tokenRes.json();
        // tokenData: { access_token, user_id }
        this.accessToken = tokenData.access_token;

        // Step 2: Exchange short-lived token for long-lived token
        const longLivedRes = await fetch(
            `https://graph.instagram.com/access_token?grant_type=ig_exchange_token&client_secret=${clientSecret}&access_token=${this.accessToken}`
        );
        if (!longLivedRes.ok) {
            throw new Error(
                `Failed to fetch long-lived Instagram token: ${longLivedRes.statusText}`
            );
        }
        const longLivedData = await longLivedRes.json();
        // longLivedData: { access_token, token_type, expires_in }
        this.accessToken = longLivedData.access_token;
        this.expiresIn = longLivedData.expires_in;
    }

    getAccessToken(): string {
        if (!this.accessToken) {
            throw new Error("Instagram access token not set.");
        }
        return this.accessToken;
    }

    setAccessToken(token: string, expiresIn?: number) {
        this.accessToken = token;
        if (expiresIn) {
            this.expiresIn = expiresIn;
        }
        // Update credentials property as well
        this.credentials.accessToken = token;
        if (expiresIn) {
            this.credentials.expiresAt = Date.now() + expiresIn * 1000;
        }
    }

    async refreshAccessToken(): Promise<void> {
        if (!this.accessToken) {
            throw new Error("No access token to refresh.");
        }
        // Instagram allows refreshing long-lived tokens
        const clientSecret = process.env.INSTAGRAM_CLIENT_SECRET!;
        const refreshRes = await fetch(
            `https://graph.instagram.com/refresh_access_token?grant_type=ig_refresh_token&access_token=${this.accessToken}`
        );
        if (!refreshRes.ok) {
            throw new Error(
                `Failed to refresh Instagram access token: ${refreshRes.statusText}`
            );
        }
        const refreshData = await refreshRes.json();
        this.accessToken = refreshData.access_token;
        this.expiresIn = refreshData.expires_in;
    }

    // ... any other auth methods ...
}
