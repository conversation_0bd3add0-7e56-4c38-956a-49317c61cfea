import {
    composeContext,
    generateText,
    getEmbeddingZeroVector,
    IAgentRuntime,
    ModelClass,
    stringToUuid,
    eliza<PERSON>ogger,
    UUI<PERSON>,
} from "@elizaos/core";
import * as fs from "fs";
import { postActionResponseFooter } from "@elizaos/core";
import { IImageDescriptionService, ServiceType } from "@elizaos/core";
import {
    ContentPlan,
    ContentPlanManager,
    ScheduledPost,
} from "./contentPlan.ts";
import { ClientBase } from "./base.ts";

export const linkedinPlanTemplate = `TASK: Generate a %days%-day content plan with approximately %num_per_day% posts per day as an array of posts in JSON format.

## VOICE AND IDENTITY
Generate posts as {{agentName}} on LinkedIn, the AI representative for this DreamStarter project.
Posts should embody these traits: {{adjective}}
Core topics focus: {{topics}}
Project phase: early concept

## CONTENT PARAMETERS
- Each post should be 10-12 sentences with 2-3 paragraphs (min 400 characters)
- Focus on professional, insightful content
- Include URL if provided in relevant posts
- Use \\n\\n between paragraphs
- Vary between: project updates, industry insights, professional development, and thought leadership
- Include relevant hashtags: 1-3 topic-specific tags
- Include #DreamStarter for all posts.

## KNOWLEDGE BASE
{{knowledge}}
{{knowledgeData}}

## ABOUT {{agentName}}
{{bio}}
{{lore}}

{{providers}}

## POST EXAMPLES (STYLE REFERENCE - This is for twitter but LinkedIn posts should be similar)
{{characterPostExamples}}

## ADDITIONAL INSTRUCTIONS
{{postDirections}}


Response should be a JSON object array inside a JSON markdown block. Correct response format:
\`\`\`json
[
  {
    "day": number,
    "content": {
        "time": string,
        "text": string
    }
  },
  {
    "day": number,
    "content": {
        "time": string,
        "text": string
    }
  },
  ...
]
\`\`\``;

export const linkedinPostTemplate = `TASK: Generate a single LinkedIn post for {{agentName}}'s AI representative.

## VOICE AND IDENTITY
Generate posts as {{agentName}} on LinkedIn, the AI representative for this DreamStarter project.
Posts should embody these traits: {{adjective}}
Core topics focus: {{topics}}
Project phase: early concept

## CONTENT PARAMETERS
- Each post should be 10-12 sentences (max 1000 characters and min 600 characters)
- Focus on professional, insightful content
- Include URL if provided in relevant posts
- Use \\n\\n between paragraphs
- Vary between: project updates, industry insights, professional development, and thought leadership
- Include relevant hashtags: 1-3 topic-specific tags
- Include #DreamStarter for all posts

## KNOWLEDGE BASE
{{knowledge}}
{{knowledgeData}}

## ABOUT {{agentName}}
{{bio}}
{{lore}}

{{providers}}

## POST EXAMPLES (STYLE REFERENCE - This is for twitter but LinkedIn posts should be similar)
{{characterPostExamples}}

## ADDITIONAL INSTRUCTIONS
{{postDirections}}
`;

export const linkedinActionTemplate =
    `
# INSTRUCTIONS: Determine actions for {{agentName}} on LinkedIn based on:
{{bio}}
{{postDirections}}

Guidelines:
- Extremely selective engagement
- Direct mentions are priority
- Skip: low-effort content, off-topic, repetitive, promotional content
- For likes: must be deeply relevant to core expertise and professional

Like Criteria (ALL must be met):
1. Content directly relates to agent's primary expertise
2. Contains substantial, meaningful professional insights
3. Aligns perfectly with agent's knowledge domain
4. Free of controversial/divisive content
5. Original content (not reposts)

Actions (respond only with tags):
[LIKE] - Perfect expertise match AND exceptional insight (10/10)
[COMMENT] - Must create meaningful professional dialogue opportunity (9.9/10)

Post:
{{currentPost}}

# Respond with qualifying action tags only.` + postActionResponseFooter;

export class LinkedInPostClient {
    client: ClientBase;

    runtime: IAgentRuntime;
    linkedinUsername: string;
    numPosts: number;
    numLikes: number;
    numComments: number;
    private isProcessing: boolean = false;
    private lastProcessTime: number = 0;
    enableActionProcessing: boolean;
    enableScheduledPosts: boolean;
    linkedinTargetUsers: string;
    postInterval: number;
    actionInterval: number;
    private postGenerationTimeoutId: NodeJS.Timeout | null;
    private actionProcessingTimeoutId: NodeJS.Timeout | null;
    contentPlanManager: ContentPlanManager;
    currentPlanId: string | null = null;
    DEFAULT_MAX_POST_LENGTH = 3000;

    constructor(client: ClientBase, runtime: IAgentRuntime) {
        this.client = client;
        this.runtime = runtime;
        this.numPosts = 0;
        this.numLikes = 0;
        this.numComments = 0;
        this.enableActionProcessing = false;
        this.linkedinUsername = this.runtime.getSetting("LINKEDIN_USERNAME");
        this.postGenerationTimeoutId = null;
        this.actionProcessingTimeoutId = null;
        this.linkedinTargetUsers = "";
        this.enableScheduledPosts = false;
        this.postInterval = 0; // 12 hours in minutes (LinkedIn recommends less frequent posting than Twitter)
        this.actionInterval = 14400000; // 4 hours in milliseconds
        this.contentPlanManager = new ContentPlanManager(client, runtime);
    }

    async start() {
        if (!this.currentPlanId) {
            const activePlan: any = await this.getActivePlan();
            if (activePlan) {
                this.currentPlanId = activePlan.id;
            } else {
                const plan = await this.generateNewPlan(new Date());
                console.log("PLAN", plan);
                this.currentPlanId = plan.id;
            }
        }

        this.startPostExecutionLoop();
    }

    private async getActivePlan() {
        // Get all plans from cache and find the active one
        const activePlanId =
            (await this.runtime.cacheManager.get(
                `linkedin/${this.client.profile.username}/active_plan`
            )) || null;

        if (!activePlanId) return null;

        const activePlan =
            (await this.runtime.cacheManager.get(
                `linkedin/${this.client.profile.username}/content_plan/${activePlanId}`
            )) || null;

        return activePlan;
    }

    private async startPostExecutionLoop() {
        const checkAndExecute = async () => {
            console.log("Checking for scheduled LinkedIn posts");
            const nextPost = await this.getNextScheduledPost();
            console.log("Next post:", nextPost);
            if (nextPost && this.shouldExecutePost(nextPost)) {
                await this.executeScheduledPost(nextPost);
            }

            // Schedule next check in 15 minutes (LinkedIn posts are less frequent)
            this.postGenerationTimeoutId = setTimeout(
                checkAndExecute,
                2 * 60 * 1000
            );
        };

        checkAndExecute();
    }

    private async getNextScheduledPost() {
        if (!this.currentPlanId) return null;

        const plan = await this.contentPlanManager.getPlan(this.currentPlanId);
        if (!plan || plan.status !== "approved") return null;

        const now = new Date();
        let sortedPosts = plan.posts
            .filter(
                (post) =>
                    post.status === "approved" &&
                    new Date(post.scheduledTime) > now
            )
            .sort(
                (a, b) =>
                    new Date(a.scheduledTime).getTime() -
                    new Date(b.scheduledTime).getTime()
            );
        const requiredPosts = this.enableScheduledPosts
            ? 5 - (sortedPosts?.length || 0)
            : 0; // LinkedIn needs fewer posts in queue
        const newPosts = [];
        for (let i = 0; i < requiredPosts; i++) {
            const newPost = await this.generateNextPost();
            if (newPost) {
                newPosts.push(newPost);
            }
            setTimeout(() => {}, 10000);
        }

        // Add new posts to the plan
        if (newPosts.length > 0) {
            // Re-sort all posts including new ones
            sortedPosts = [
                ...sortedPosts,
                ...newPosts.filter(
                    (post) =>
                        post.status === "approved" &&
                        new Date(post.scheduledTime) > now
                ),
            ]
                .filter(
                    (post) =>
                        post.status === "approved" &&
                        new Date(post.scheduledTime) > now
                )
                .sort(
                    (a, b) =>
                        new Date(a.scheduledTime).getTime() -
                        new Date(b.scheduledTime).getTime()
                );
        }

        return sortedPosts[0];
    }

    async recalculatePostSchedule(
        plan: ContentPlan,
        newInterval: number
    ): Promise<void> {
        // if (!plan) return;
        // const now = new Date();
        // const updatedPlan = structuredClone(plan);
        // const newPosts = [];
        // const approvedPosts = plan.posts.filter(
        //     (post) =>
        //         post.status === "approved" && new Date(post.scheduledTime) > now
        // );
        // if (approvedPosts.length < 5) {
        //     // LinkedIn needs fewer posts
        //     const requiredPosts = 5 - (approvedPosts?.length || 0);
        //     for (let i = 0; i < requiredPosts; i++) {
        //         const newPost = await this.generateNextPost();
        //         if (newPost) {
        //             newPosts.push(newPost);
        //         }
        //         setTimeout(() => {}, 10000);
        //     }
        //     if (newPosts.length > 0) {
        //         updatedPlan.posts = [
        //             ...updatedPlan.posts,
        //             ...newPosts.filter(
        //                 (post) =>
        //                     post.status === "approved" &&
        //                     new Date(post.scheduledTime) > now
        //             ),
        //         ]
        //             .filter(
        //                 (post) =>
        //                     post.status === "approved" &&
        //                     new Date(post.scheduledTime) > now
        //             )
        //             .sort(
        //                 (a, b) =>
        //                     new Date(a.scheduledTime).getTime() -
        //                     new Date(b.scheduledTime).getTime()
        //             );
        //     }
        // }
        // updatedPlan.posts
        //     .filter(
        //         (post) =>
        //             post.status === "approved" &&
        //             new Date(post.scheduledTime) > now
        //     )
        //     .sort(
        //         (a, b) =>
        //             new Date(a.scheduledTime).getTime() -
        //             new Date(b.scheduledTime).getTime()
        //     );
        // const baseTime = new Date(updatedPlan.posts[0].scheduledTime);
        // const isStartingFromNow = baseTime < now;
        // for (let i = 0; i < updatedPlan.posts.length; i++) {
        //     if (i === 0 && !isStartingFromNow) return;
        //     const newScheduledTime = new Date(baseTime);
        //     newScheduledTime.setMinutes(
        //         newScheduledTime.getMinutes() + i * newInterval
        //     );
        //     updatedPlan.posts[i].scheduledTime = newScheduledTime;
        // }
        // await this.contentPlanManager.storePlan(updatedPlan);
    }

    private shouldExecutePost(post: ScheduledPost): boolean {
        const now = new Date();
        const scheduledTime = new Date(post.scheduledTime);
        console.log("LinkedIn USER", this.client.profile.username);
        console.log(
            "SHOULD execute",
            Math.abs(now.getTime() - scheduledTime.getTime()) <= 15 * 60 * 1000
        );
        return (
            Math.abs(now.getTime() - scheduledTime.getTime()) <= 15 * 60 * 1000
        );
    }

    private async executeScheduledPost(post: ScheduledPost) {
        try {
            elizaLogger.log(`Executing scheduled LinkedIn post: ${post.id}`);

            const roomId = stringToUuid(
                "linkedin_generate_room-" + this.client.profile.username
            );

            // Check if there's an attachment and pass it to postToLinkedIn
            const attachment =
                post.attachments?.length && post.localPath?.length
                    ? {
                          type: post.localPath[0].type,
                          url: post.localPath[0].url,
                      }
                    : null;

            await this.postToLinkedIn(
                this.runtime,
                this.client,
                post.content,
                roomId,
                this.linkedinUsername,
                attachment
            );

            // Update post status
            await this.contentPlanManager.updatePost(
                this.currentPlanId,
                post.id,
                {
                    status: "posted",
                }
            );
            if (this.enableScheduledPosts) {
                await this.generateNextPost();
            }
        } catch (error) {
            elizaLogger.error(
                `Error executing scheduled LinkedIn post ${post.id}:`,
                error
            );
        }
    }

    private async generateNextPost() {
        if (!this.currentPlanId) return;

        const plan = await this.contentPlanManager.getPlan(this.currentPlanId);
        if (!plan) return;

        const nextPost = await this.contentPlanManager.generateNextPost(
            plan,
            this.postInterval
        );
        if (nextPost) {
            // Add the new post to the plan
            plan.posts.push(nextPost);
            plan.metadata.totalPosts = plan.posts.length;
            await this.contentPlanManager.storePlan(plan);
            elizaLogger.log(
                `Generated next LinkedIn post for time: ${nextPost.scheduledTime}`
            );
            return nextPost;
        }
    }

    // New methods for content plan management
    async generateNewPlan(startDate: Date = new Date()): Promise<ContentPlan> {
        return await this.contentPlanManager.generateContentPlan(
            startDate,
            30,
            this.postInterval,
            this.enableScheduledPosts
        );
    }

    async stopNewPosts() {
        // Stop post generation loop
        if (this.postGenerationTimeoutId) {
            clearTimeout(this.postGenerationTimeoutId);
            this.postGenerationTimeoutId = null;
            elizaLogger.log("LinkedIn post generation loop stopped");
        }
    }

    // startProcessingActions() {
    //     this.enableActionProcessing = true;
    //     const processActionsLoop = async () => {
    //         if (!this.enableActionProcessing) {
    //             elizaLogger.log("LinkedIn action processing stopped");
    //             return;
    //         }

    //         try {
    //             const results = await this.processLinkedInActions();
    //             if (results) {
    //                 elizaLogger.log(`Processed ${results.length} LinkedIn posts/activities`);
    //                 elizaLogger.log(
    //                     `Next action processing scheduled in ${this.actionInterval / 1000} seconds`
    //                 );
    //             }
    //         } catch (error) {
    //             elizaLogger.error("Error in LinkedIn action processing loop:", error);
    //         }

    //         // Only schedule next iteration if processing is still enabled
    //         if (this.enableActionProcessing) {
    //             this.actionProcessingTimeoutId = setTimeout(
    //                 processActionsLoop,
    //                 this.actionInterval
    //             );
    //         }
    //     };

    //     if (!this.isDryRun) {
    //         processActionsLoop().catch((error) => {
    //             elizaLogger.error(
    //                 "Fatal error in LinkedIn process actions loop:",
    //                 error
    //             );
    //         });
    //     } else {
    //         if (this.isDryRun) {
    //             elizaLogger.log(
    //                 "LinkedIn action processing loop disabled (dry run mode)"
    //             );
    //         } else {
    //             elizaLogger.log(
    //                 "LinkedIn action processing loop disabled by configuration"
    //             );
    //         }
    //     }
    // }

    async stop() {
        // Stop action processing
        this.enableActionProcessing = false;
        if (this.actionProcessingTimeoutId) {
            clearTimeout(this.actionProcessingTimeoutId);
            this.actionProcessingTimeoutId = null;
        }
        elizaLogger.log("LinkedIn action processing stopped");
    }

    // Creates a LinkedIn post object from API result
    createLinkedInObject(postResult: any, client: any): any {
        return {
            id: postResult.id,
            name: client.profile.fullName || client.profile.name,
            username: client.profile.username,
            text: postResult.text || postResult.commentary,
            permalink:
                postResult.permalink ||
                `https://www.linkedin.com/feed/update/${postResult.id}`,
            createdAt: postResult.createdAt || new Date().toISOString(),
            timestamp: postResult.timestamp || Date.now(),
            userId: client.profile.id,
            photos: postResult.photos || [],
            videos: postResult.videos || [],
        };
    }

    // async processAndCacheLinkedInPost(
    //     runtime: IAgentRuntime,
    //     client: ClientBase,
    //     post: any,
    //     roomId: UUID,
    //     newPostContent: string
    // ) {
    //     // Cache the last post details
    //     await runtime.cacheManager.set(
    //         `linkedin/${client.profile.username}/lastPost`,
    //         {
    //             id: post.id,
    //             timestamp: Date.now(),
    //         }
    //     );

    //     // Cache the post
    //     await client.cacheLinkedInPost(post);

    //     // Log the posted content
    //     elizaLogger.log(`LinkedIn post published:\n ${post.permalink}`);

    //     // Ensure the room and participant exist
    //     await runtime.ensureRoomExists(roomId);
    //     await runtime.ensureParticipantInRoom(runtime.agentId, roomId);

    //     // Create a memory for the post
    //     await runtime.messageManager.createMemory({
    //         id: stringToUuid(post.id + "-" + runtime.agentId),
    //         userId: runtime.agentId,
    //         agentId: runtime.agentId,
    //         content: {
    //             text: newPostContent.trim(),
    //             url: post.permalink,
    //             source: "linkedin",
    //         },
    //         roomId,
    //         embedding: getEmbeddingZeroVector(),
    //         createdAt: post.timestamp,
    //     });
    // }

    async postToLinkedIn(
        runtime: IAgentRuntime,
        client: ClientBase,
        postContent: string,
        roomId: UUID,
        linkedinUsername: string,
        attachments?: {
            type: string;
            url: string;
        } | null
    ) {
        try {
            elizaLogger.log(
                `Posting new LinkedIn content for:\n ${linkedinUsername}`
            );

            let mediaIds = [];

            // Handle attachments if present
            // if (attachments?.url) {
            //     try {
            //         const mediaData = fs.readFileSync(attachments.url);
            //         const mediaType = attachments.type;

            //         // Upload the media to LinkedIn and get media ID
            //         const mediaId = await client.linkedinClient.uploadMedia(mediaData, mediaType);
            //         if (mediaId) {
            //             mediaIds.push(mediaId);
            //         }
            //     } catch (error) {
            //         elizaLogger.error("Error processing LinkedIn attachment:", error);
            //     }
            // }

            // Send the post to LinkedIn
            await client.requestQueue.add(async () => {
                elizaLogger.log(
                    `Sending LinkedIn post with${
                        attachments ? "" : "out"
                    } image`
                );
                if (attachments) {
                    elizaLogger.log(`Image path: ${attachments.url}`);
                }

                return await runtime.clients.linkedin.api.createPost(
                    this.client.profile.type,
                    this.client.profile.id,
                    postContent,
                    attachments?.url
                );
            });

            // if (!postResult || postResult.error) {
            //     elizaLogger.error(
            //         "LinkedIn API error:",
            //         postResult?.error || "Unknown error"
            //     );
            //     return;
            // }

            // const linkedinPost = this.createLinkedInObject(postResult, client);

            // await this.processAndCacheLinkedInPost(
            //     runtime,
            //     client,
            //     linkedinPost,
            //     roomId,
            //     newPostContent
            // );

            this.numPosts++;
        } catch (error) {
            elizaLogger.error("Error sending LinkedIn post:", error);
            throw error;
        }
    }

    /**
     * Generates and posts a new LinkedIn post. If isDryRun is true, only logs what would have been posted.
     */
    // private async generateNewLinkedInPost() {
    //     elizaLogger.log("Generating new LinkedIn post");

    //     try {
    //         const roomId = stringToUuid(
    //             "linkedin_generate_room-" + this.client.profile.username
    //         );
    //         await this.runtime.ensureUserExists(
    //             this.runtime.agentId,
    //             this.client.profile.username,
    //             this.runtime.character.name,
    //             "linkedin"
    //         );

    //         const topics = this.runtime.character.topics.join(", ");

    //         const state = await this.runtime.composeState(
    //             {
    //                 userId: this.runtime.agentId,
    //                 roomId: roomId,
    //                 agentId: this.runtime.agentId,
    //                 content: {
    //                     text: topics || "",
    //                     action: "POST_LINKEDIN",
    //                 },
    //             },
    //             {
    //                 linkedinUsername: this.client.profile.username,
    //             }
    //         );

    //         const context = composeContext({
    //             state,
    //             template:
    //                 this.runtime.character.templates?.linkedinPostTemplate ||
    //                 linkedinPostTemplate,
    //         });

    //         elizaLogger.debug("generate LinkedIn post prompt:\n" + context);

    //         const newPostContent = await generateText({
    //             runtime: this.runtime,
    //             context,
    //             modelClass: ModelClass.MEDIUM,
    //         });

    //         // First attempt to clean content
    //         let cleanedContent = "";

    //         // Try parsing as JSON first
    //         try {
    //             const parsedResponse = JSON.parse(newPostContent);
    //             if (parsedResponse.text) {
    //                 cleanedContent = parsedResponse.text;
    //             } else if (typeof parsedResponse === "string") {
    //                 cleanedContent = parsedResponse;
    //             }
    //         } catch (error) {
    //             // If not JSON, clean the raw content
    //             cleanedContent = newPostContent
    //                 .replace(/^\s*{?\s*"text":\s*"|"\s*}?\s*$/g, "") // Remove JSON-like wrapper
    //                 .replace(/^['"](.*)['"]$/g, "$1") // Remove quotes
    //                 .replace(/\\"/g, '"') // Unescape quotes
    //                 .replace(/\\n/g, "\n") // Unescape newlines
    //                 .trim();
    //         }

    //         if (!cleanedContent) {
    //             elizaLogger.error(
    //                 "Failed to extract valid content from LinkedIn response:",
    //                 {
    //                     rawResponse: newPostContent,
    //                     attempted: "JSON parsing",
    //                 }
    //             );
    //             return;
    //         }

    //         const removeQuotes = (str: string) =>
    //             str.replace(/^['"](.*)['"]$/, "$1");

    //         const fixNewLines = (str: string) => str.replaceAll(/\\n/g, "\n");

    //         // Final cleaning
    //         cleanedContent = removeQuotes(fixNewLines(cleanedContent));

    //         if (this.isDryRun) {
    //             elizaLogger.info(
    //                 `Dry run: would have posted to LinkedIn: ${cleanedContent}`
    //             );
    //             return;
    //         }

    //         try {
    //             elizaLogger.log(`Posting new LinkedIn post:\n ${cleanedContent}`);
    //             this.postToLinkedIn(
    //                 this.runtime,
    //                 this.client,
    //                 cleanedContent,
    //                 roomId,
    //                 newPostContent,
    //                 this.linkedinUsername
    //             );
    //         } catch (error) {
    //             elizaLogger.error("Error sending LinkedIn post:", error);
    //         }
    //     } catch (error) {
    //         elizaLogger.error("Error generating new LinkedIn post:", error);
    //     }
    // }

    // private async generateLinkedInContent(
    //     postState: any,
    //     options?: {
    //         template?: string;
    //         context?: string;
    //     }
    // ): Promise<string> {
    //     const context = composeContext({
    //         state: postState,
    //         template:
    //             options?.template ||
    //             this.runtime.character.templates?.linkedinPostTemplate ||
    //             linkedinPostTemplate,
    //     });

    //     const response = await generateText({
    //         runtime: this.runtime,
    //         context: options?.context || context,
    //         modelClass: ModelClass.LARGE,
    //     });
    //     elizaLogger.debug("generate LinkedIn content response:\n" + response);

    //     // First clean up any markdown and newlines
    //     const cleanedResponse = response
    //         .replace(/```json\s*/g, "") // Remove ```json
    //         .replace(/```\s*/g, "") // Remove any remaining ```
    //         .replaceAll(/\\n/g, "\n")
    //         .trim();

    //     // Try to parse as JSON first
    //     try {
    //         const jsonResponse = JSON.parse(cleanedResponse);
    //         if (jsonResponse.text) {
    //             return this.trimPostLength(jsonResponse.text);
    //         }
    //         if (typeof jsonResponse === "object") {
    //             const possibleContent =
    //                 jsonResponse.content ||
    //                 jsonResponse.message ||
    //                 jsonResponse.response;
    //             if (possibleContent) {
    //                 return this.trimPostLength(possibleContent);
    //             }
    //         }
    //     } catch (error) {
    //         // If JSON parsing fails, treat as plain text
    //         elizaLogger.debug("Response is not JSON, treating as plain text");
    //     }

    //     // If not JSON or no valid content found, clean the raw text
    //     return this.trimPostLength(cleanedResponse);
    // }

    // Helper method to ensure post length compliance
    private trimPostLength(text: string, maxLength: number = 3000): string {
        if (text.length <= maxLength) return text;

        // Try to cut at last paragraph
        const lastParagraph = text.slice(0, maxLength).lastIndexOf("\n\n");
        if (lastParagraph > 0) {
            return text.slice(0, lastParagraph).trim();
        }

        // Try to cut at last sentence
        const lastSentence = text.slice(0, maxLength).lastIndexOf(".");
        if (lastSentence > 0) {
            return text.slice(0, lastSentence + 1).trim();
        }

        // Fallback to word boundary
        return (
            text.slice(0, text.lastIndexOf(" ", maxLength - 3)).trim() + "..."
        );
    }

    /**
     * Processes LinkedIn actions (likes, comments). If isDryRun is true,
     * only simulates and logs actions without making API calls.
     */
    // private async processLinkedInActions() {
    //     if (this.isProcessing) {
    //         elizaLogger.log("Already processing LinkedIn actions, skipping");
    //         return null;
    //     }

    //     try {
    //         this.isProcessing = true;
    //         this.lastProcessTime = Date.now();

    //         elizaLogger.log("Processing LinkedIn actions");

    //         if (this.isDryRun) {
    //             elizaLogger.log("Dry run mode: simulating LinkedIn actions");
    //             return [];
    //         }

    //         await this.runtime.ensureUserExists(
    //             this.runtime.agentId,
    //             this.linkedinUsername,
    //             this.runtime.character.name,
    //             "linkedin"
    //         );

    //         // Process target users if configured
    //         if (this.linkedinTargetUsers) {
    //             if (!this.enableActionProcessing) {
    //                 return [];
    //             }

    //             const TARGET_USERS = [
    //                 "DreamStarterXYZ",
    //                 ...this.linkedinTargetUsers.split(","),
    //             ];

    //             elizaLogger.log("Processing LinkedIn target users:", TARGET_USERS);

    //             if (TARGET_USERS.length > 0) {
    //                 for (const username of TARGET_USERS) {
    //                     try {
    //                         const userPosts = await this.client.linkedinClient.fetchUserPosts(username, 5);

    //                         if (userPosts && userPosts.length > 0) {
    //                             elizaLogger.log(`Found ${userPosts.length} valid LinkedIn posts from ${username}`);

    //                             for (const post of userPosts) {
    //                                 if (!this.canPerformAction("like")) {
    //                                     break;
    //                                 }

    //                                 await this.client.linkedinClient.likePost(post.id);
    //                                 elizaLogger.log(`Liked LinkedIn post ${post.id}`);
    //                                 this.numLikes++;
    //                                 await this.updateActionCounter("like");
    //                                 await new Promise(resolve => setTimeout(resolve, 30000)); // Wait 30 seconds between likes
    //                             }
    //                         }
    //                     } catch (error) {
    //                         elizaLogger.error(`Error fetching LinkedIn posts for ${username}:`, error);
    //                         continue;
    //                     }
    //                 }
    //             }
    //         }

    //         // Process feed/timeline for engagement
    //         const feedPosts = await this.client.linkedinClient.fetchFeedPosts(10);
    //         const results = [];

    //         for (const post of feedPosts) {
    //             if (!this.enableActionProcessing) {
    //                 elizaLogger.log("LinkedIn action processing disabled, skipping");
    //                 break;
    //             }

    //             try {
    //                 // Skip if we've already processed this post
    //                 const memory = await this.runtime.messageManager.getMemoryById(
    //                     stringToUuid(post.id + "-" + this.runtime.agentId)
    //                 );
    //                 if (memory) {
    //                     elizaLogger.log(`Already processed LinkedIn post ID: ${post.id}`);
    //                     continue;
    //                 }

    //                 // Skip own posts
    //                 if (post.author && post.author.username === this.linkedinUsername) {
    //                     elizaLogger.log(`Skipping own LinkedIn post: ${post.id}`);
    //                     continue;
    //                 }

    //                 const roomId = stringToUuid(
    //                     post.id + "-" + this.runtime.agentId
    //                 );

    //                 const postState = await this.runtime.composeState(
    //                     {
    //                         userId: this.runtime.agentId,
    //                         roomId,
    //                         agentId: this.runtime.agentId,
    //                         content: { text: "", action: "" },
    //                     },
    //                     {
    //                         linkedinUsername: this.linkedinUsername,
    //                         currentPost: `ID: ${post.id}\nFrom: ${post.author.name} (${post.author.username})\nContent: ${post.text}`,
    //                     }
    //                 );

    //                 const actionContext = composeContext({
    //                     state: postState,
    //                     template:
    //                         this.runtime.character.templates
    //                             ?.linkedinActionTemplate ||
    //                         linkedinActionTemplate,
    //                 });

    //                 const actionResponse = await generateTweetActions({
    //                     runtime: this.runtime,
    //                     context: actionContext,
    //                     modelClass: ModelClass.MEDIUM,
    //                 });

    //                 if (
    //                     !actionResponse ||
    //                     Object.keys(actionResponse).length === 0
    //                 ) {
    //                     elizaLogger.log(
    //                         `No valid actions generated for LinkedIn post ${post.id}`
    //                     );
    //                     await this.createSkippedPostMemory(post, roomId);
    //                     continue;
    //                 }

    //                 const executedActions: string[] = [];

    //                 // Execute actions
    //                 if (actionResponse.like && this.canPerformAction("like")) {
    //                     try {
    //                         if (this.isDryRun) {
    //                             elizaLogger.info(
    //                                 `Dry run: would have liked LinkedIn post ${post.id}`
    //                             );
    //                             executedActions.push("like (dry run)");
    //                         } else {
    //                             await this.client.linkedinClient.likePost(
    //                                 post.id
    //                             );
    //                             executedActions.push("like");
    //                             elizaLogger.log(`Liked LinkedIn post ${post.id}`);
    //                             this.numLikes++;
    //                             await this.updateActionCounter("like");
    //                             await new Promise(
    //                                 (resolve) =>
    //                                     setTimeout(
    //                                         resolve,
    //                                         30000 // 30 seconds between actions
    //                                     )
    //                             );
    //                         }
    //                     } catch (error) {
    //                         elizaLogger.error(
    //                             `Error liking LinkedIn post ${post.id}:`,
    //                             error
    //                         );
    //                     }
    //                 }

    //                 if (actionResponse.comment && this.canPerformAction("comment")) {
    //                     try {
    //                         await this.handleCommentOnPost(
    //                             post,
    //                             postState,
    //                             executedActions
    //                         );
    //                     } catch (error) {
    //                         elizaLogger.error(
    //                             `Error commenting on LinkedIn post ${post.id}:`,
    //                             error
    //                         );
    //                     }
    //                 }

    //                 // Add these checks before creating memory
    //                 await this.runtime.ensureRoomExists(roomId);
    //                 await this.runtime.ensureUserExists(
    //                     stringToUuid(post.author.id),
    //                     post.author.username,
    //                     post.author.name,
    //                     "linkedin"
    //                 );
    //                 await this.runtime.ensureParticipantInRoom(
    //                     this.runtime.agentId,
    //                     roomId
    //                 );

    //                 // Create memory for the processed post
    //                 await this.runtime.messageManager.createMemory({
    //                     id: stringToUuid(post.id + "-" + this.runtime.agentId),
    //                     userId: stringToUuid(post.author.id),
    //                     content: {
    //                         text: post.text,
    //                         url: post.permalink,
    //                         source: "linkedin",
    //                         action: executedActions.join(","),
    //                     },
    //                     agentId: this.runtime.agentId,
    //                     roomId,
    //                     embedding: getEmbeddingZeroVector(),
    //                     createdAt: post.timestamp,
    //                 });

    //                 results.push({
    //                     postId: post.id,
    //                     parsedActions: actionResponse,
    //                     executedActions,
    //                 });
    //             } catch (error) {
    //                 elizaLogger.error(
    //                     `Error processing LinkedIn post ${post.id}:`,
    //                     error
    //                 );
    //                 continue;
    //             }
    //         }

    //         return results;
    //     } catch (error) {
    //         elizaLogger.error("Error in processLinkedInActions:", error);
    //         throw error;
    //     } finally {
    //         this.isProcessing = false;
    //     }
    // }

    // private async handleTextOnlyReply(
    //     tweet: Tweet,
    //     tweetState: any,
    //     executedActions: string[]
    // ) {
    //     try {
    //         // Build conversation thread for context
    //         const thread = await buildConversationThread(tweet, this.client);
    //         const formattedConversation = thread
    //             .map(
    //                 (t) =>
    //                     `@${t.username} (${new Date(t.timestamp * 1000).toLocaleString()}): ${t.text}`
    //             )
    //             .join("\n\n");

    //         // Generate image descriptions if present
    //         const imageDescriptions = [];
    //         if (tweet.photos?.length > 0) {
    //             elizaLogger.log("Processing images in tweet for context");
    //             for (const photo of tweet.photos) {
    //                 const description = await this.runtime
    //                     .getService<IImageDescriptionService>(
    //                         ServiceType.IMAGE_DESCRIPTION
    //                     )
    //                     .describeImage(photo.url);
    //                 imageDescriptions.push(description);
    //             }
    //         }

    //         // Handle quoted tweet if present
    //         let quotedContent = "";
    //         if (tweet.quotedStatusId) {
    //             try {
    //                 const quotedTweet =
    //                     await this.client.twitterClient.getTweet(
    //                         tweet.quotedStatusId
    //                     );
    //                 if (quotedTweet) {
    //                     quotedContent = `\nQuoted Tweet from @${quotedTweet.username}:\n${quotedTweet.text}`;
    //                 }
    //             } catch (error) {
    //                 elizaLogger.error("Error fetching quoted tweet:", error);
    //             }
    //         }

    //         // Compose rich state with all context
    //         const enrichedState = await this.runtime.composeState(
    //             {
    //                 userId: this.runtime.agentId,
    //                 roomId: stringToUuid(
    //                     tweet.conversationId + "-" + this.runtime.agentId
    //                 ),
    //                 agentId: this.runtime.agentId,
    //                 content: { text: tweet.text, action: "" },
    //             },
    //             {
    //                 twitterUserName: this.twitterUsername,
    //                 currentPost: `From @${tweet.username}: ${tweet.text}`,
    //                 formattedConversation,
    //                 imageContext:
    //                     imageDescriptions.length > 0
    //                         ? `\nImages in Tweet:\n${imageDescriptions.map((desc, i) => `Image ${i + 1}: ${desc}`).join("\n")}`
    //                         : "",
    //                 quotedContent,
    //             }
    //         );

    //         // Generate and clean the reply content
    //         const replyText = await this.generateTweetContent(enrichedState, {
    //             template:
    //                 this.runtime.character.templates
    //                     ?.twitterMessageHandlerTemplate ||
    //                 twitterMessageHandlerTemplate,
    //         });

    //         if (!replyText) {
    //             elizaLogger.error("Failed to generate valid reply content");
    //             return;
    //         }

    //         if (this.isDryRun) {
    //             elizaLogger.info(
    //                 `Dry run: reply to tweet ${tweet.id} would have been: ${replyText}`
    //             );
    //             executedActions.push("reply (dry run)");
    //             return;
    //         }

    //         elizaLogger.debug("Final reply text to be sent:", replyText);

    //         let result;

    //         if (replyText.length > DEFAULT_MAX_TWEET_LENGTH) {
    //             result = await this.handleNoteTweet(
    //                 this.client,
    //                 this.runtime,
    //                 replyText,
    //                 tweet.id
    //             );
    //         } else {
    //             result = await this.sendStandardTweet(
    //                 this.client,
    //                 replyText,
    //                 tweet.id
    //             );
    //         }

    //         if (result) {
    //             elizaLogger.log("Successfully posted reply tweet");
    //             executedActions.push("reply");

    //             // Cache generation context for debugging
    //             await this.runtime.cacheManager.set(
    //                 `twitter/reply_generation_${tweet.id}.txt`,
    //                 `Context:\n${enrichedState}\n\nGenerated Reply:\n${replyText}`
    //             );
    //             this.numReplies++;
    //             await this.updateActionCounter("reply");
    //         } else {
    //             elizaLogger.error("Tweet reply creation failed");
    //         }
    //     } catch (error) {
    //         elizaLogger.error("Error in handleTextOnlyReply:", error);
    //     }
    // }

    // // Helper method to create memory for skipped tweets
    // private async createSkippedTweetMemory(tweet: Tweet, roomId: UUID) {
    //     await this.runtime.ensureRoomExists(roomId);
    //     await this.runtime.ensureUserExists(
    //         stringToUuid(tweet.userId),
    //         tweet.username,
    //         tweet.name,
    //         "twitter"
    //     );
    //     await this.runtime.ensureParticipantInRoom(
    //         this.runtime.agentId,
    //         roomId
    //     );

    //     await this.runtime.messageManager.createMemory({
    //         id: stringToUuid(tweet.id + "-" + this.runtime.agentId),
    //         userId: stringToUuid(tweet.userId),
    //         content: {
    //             text: tweet.text,
    //             url: tweet.permanentUrl,
    //             source: "twitter",
    //             action: "skipped",
    //         },
    //         agentId: this.runtime.agentId,
    //         roomId,
    //         embedding: getEmbeddingZeroVector(),
    //         createdAt: tweet.timestamp * 1000,
    //     });
    // }

    // // Helper method to create memory for processed tweets
    // private async createProcessedTweetMemory(
    //     tweet: Tweet,
    //     roomId: UUID,
    //     executedActions: string[]
    // ) {
    //     await this.runtime.ensureRoomExists(roomId);
    //     await this.runtime.ensureUserExists(
    //         stringToUuid(tweet.userId),
    //         tweet.username,
    //         tweet.name,
    //         "twitter"
    //     );
    //     await this.runtime.ensureParticipantInRoom(
    //         this.runtime.agentId,
    //         roomId
    //     );

    //     await this.runtime.messageManager.createMemory({
    //         id: stringToUuid(tweet.id + "-" + this.runtime.agentId),
    //         userId: stringToUuid(tweet.userId),
    //         content: {
    //             text: tweet.text,
    //             url: tweet.permanentUrl,
    //             source: "twitter",
    //             action: executedActions.join(","),
    //         },
    //         agentId: this.runtime.agentId,
    //         roomId,
    //         embedding: getEmbeddingZeroVector(),
    //         createdAt: tweet.timestamp * 1000,
    //     });
    // }

    // private actionCounts: {
    //     [key: string]: { count: number; lastReset: number };
    // } = {
    //     like: { count: 0, lastReset: Date.now() },
    //     retweet: { count: 0, lastReset: Date.now() },
    //     reply: { count: 0, lastReset: Date.now() },
    //     quote: { count: 0, lastReset: Date.now() },
    // };

    // private readonly ACTION_LIMITS = {
    //     like: { max: 8, windowHours: 3 },
    //     retweet: { max: 5, windowHours: 3 },
    //     reply: { max: 8, windowHours: 3 },
    //     quote: { max: 5, windowHours: 3 },
    // };

    // private canPerformAction(actionType: string): boolean {
    //     const now = Date.now();
    //     const actionState = this.actionCounts[actionType];
    //     const limit =
    //         this.ACTION_LIMITS[actionType as keyof typeof this.ACTION_LIMITS];

    //     if (!actionState || !limit) {
    //         return false;
    //     }

    //     // Reset counter if window has passed
    //     const windowMs = limit.windowHours * 60 * 60 * 1000;
    //     if (now - actionState.lastReset >= windowMs) {
    //         actionState.count = 0;
    //         actionState.lastReset = now;
    //     }

    //     return actionState.count < limit.max;
    // }

    // private async updateActionCounter(actionType: string): Promise<void> {
    //     const actionState = this.actionCounts[actionType];
    //     if (actionState) {
    //         actionState.count++;

    //         // Store the updated count in cache for persistence
    //         await this.runtime.cacheManager.set(
    //             `twitter/${this.twitterUsername}/action_counts/${actionType}`,
    //             {
    //                 count: actionState.count,
    //                 lastReset: actionState.lastReset,
    //             }
    //         );
    //     }
    // }
}
