/* eslint-disable no-shadow */
import { Dict } from 'mixpanel-browser';

export enum MixpanelEventName {
    // Page Navigation
    pageView = 'Page View',

    // Authentication Events
    signUp = 'Sign Up',
    signIn = 'Sign In',
    signOut = 'Sign Out',
    authError = 'Authentication Error',

    // Project Management
    projectCreated = 'Project Created',
    projectSelected = 'Project Selected',
    projectDeleted = 'Project Deleted',
    projectUpdated = 'Project Updated',

    // Account Connections
    accountConnected = 'Account Connected',
    accountDisconnected = 'Account Disconnected',
    accountConnectionError = 'Account Connection Error',
    twitterPremiumUpgrade = 'Twitter Premium Upgrade',
    organizationAccessGranted = 'Organization Access Granted',

    // Content Creation & Management
    postCreated = 'Post Created',
    postScheduled = 'Post Scheduled',
    postUpdated = 'Post Updated',
    postDeleted = 'Post Deleted',
    contentGenerated = 'Content Generated',
    imageGenerated = 'Image Generated',

    // Bot Management
    botCreated = 'Bot Created',
    botStarted = 'Bot Started',
    botStopped = 'Bot Stopped',
    botDeleted = 'Bot Deleted',

    // Knowledge Base
    knowledgeAdded = 'Knowledge Added',
    knowledgeDeleted = 'Knowledge Deleted',
    knowledgeUpdated = 'Knowledge Updated',

    // Analytics & Insights
    analyticsViewed = 'Analytics Viewed',
    calendarViewed = 'Calendar Viewed',

    // Settings & Configuration
    settingsUpdated = 'Settings Updated',
    billingUpdated = 'Billing Updated',

    // Errors & Issues
    errorOccurred = 'Error Occurred',
    apiError = 'API Error',

    // Legacy Events (keeping for backward compatibility)
    chatEvent = 'Chat Event',
    tradeTokenEvent = 'Trade Token Event',
    createTokenEvent = 'Create Token Event',
}

export type CustomEventProps = {
    mixpanelProps: Dict,
    eventName: MixpanelEventName,
}

// Common event properties for consistency
export interface BaseEventProps {
    userId?: string;
    projectId?: string;
    platform?: string;
    timestamp?: number;
    userAgent?: string;
    url?: string;
    referrer?: string;
}

export interface AuthEventProps extends BaseEventProps {
    method?: 'email' | 'google' | 'github';
    isNewUser?: boolean;
}

export interface ProjectEventProps extends BaseEventProps {
    projectName?: string;
    billingStatus?: string;
    accountsCount?: number;
}

export interface AccountEventProps extends BaseEventProps {
    platform: string;
    username?: string;
    hasOrganizationAccess?: boolean;
    hasPremium?: boolean;
    connectionMethod?: string;
}

export interface ContentEventProps extends BaseEventProps {
    contentType?: 'text' | 'image' | 'video';
    platforms?: string[];
    scheduledTime?: string;
    isAIGenerated?: boolean;
    characterCount?: number;
}

export interface ErrorEventProps extends BaseEventProps {
    errorMessage?: string;
    errorCode?: string;
    stackTrace?: string;
    component?: string;
}
