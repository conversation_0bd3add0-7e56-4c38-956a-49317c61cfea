import { MixpanelEventName } from '@/common/utils/mixpanel/types';

/**
 * Higher-order component that wraps a button with analytics tracking
 */
export function withButtonTracking<T extends React.ComponentProps<'button'>>(
  Component: React.ComponentType<T>,
  eventName: string,
  location: string
) {
  return function TrackedButton(props: T & { analytics?: any }) {
    const { analytics, onClick, ...rest } = props;

    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
      // Track the button click
      if (analytics?.trackButtonClick) {
        analytics.trackButtonClick(eventName, location, {
          buttonText: typeof props.children === 'string' ? props.children : undefined,
          disabled: props.disabled,
        });
      }

      // Call the original onClick handler
      if (onClick) {
        onClick(event);
      }
    };

    return <Component {...(rest as T)} onClick={handleClick} />;
  };
}

/**
 * Higher-order component that wraps a form with analytics tracking
 */
export function withFormTracking<T extends React.ComponentProps<'form'>>(
  Component: React.ComponentType<T>,
  formName: string
) {
  return function TrackedForm(props: T & { analytics?: any }) {
    const { analytics, onSubmit, ...rest } = props;

    const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
      let success = true;
      let error: Error | null = null;

      try {
        // Call the original onSubmit handler
        if (onSubmit) {
          await onSubmit(event);
        }
      } catch (err) {
        success = false;
        error = err instanceof Error ? err : new Error('Form submission failed');
        throw err; // Re-throw to maintain original behavior
      } finally {
        // Track the form submission
        if (analytics?.trackFormSubmission) {
          analytics.trackFormSubmission(formName, success, {
            errorMessage: error?.message,
          });
        }
      }
    };

    return <Component {...(rest as T)} onSubmit={handleSubmit} />;
  };
}

/**
 * Utility function to track API calls
 */
export function trackApiCall(
  analytics: any,
  endpoint: string,
  method: string,
  success: boolean,
  statusCode?: number,
  error?: string,
  duration?: number
) {
  if (!analytics?.trackEvent) return;

  analytics.trackEvent(MixpanelEventName.pageView, {
    eventType: 'api_call',
    endpoint,
    method,
    success,
    statusCode,
    errorMessage: error,
    duration,
  });
}

/**
 * Utility function to track feature usage
 */
export function trackFeatureUsage(
  analytics: any,
  featureName: string,
  action: string,
  additionalProps?: Record<string, any>
) {
  if (!analytics?.trackEvent) return;

  analytics.trackEvent(MixpanelEventName.pageView, {
    eventType: 'feature_usage',
    featureName,
    action,
    ...additionalProps,
  });
}

/**
 * Utility function to track user engagement
 */
export function trackEngagement(
  analytics: any,
  engagementType: 'scroll' | 'click' | 'hover' | 'focus' | 'time_spent',
  element?: string,
  value?: number,
  additionalProps?: Record<string, any>
) {
  if (!analytics?.trackEvent) return;

  analytics.trackEvent(MixpanelEventName.pageView, {
    eventType: 'engagement',
    engagementType,
    element,
    value,
    ...additionalProps,
  });
}

/**
 * Hook for tracking time spent on page
 */
export function useTimeTracking(analytics: any, pageName: string) {
  const startTime = Date.now();

  const trackTimeSpent = () => {
    const timeSpent = Date.now() - startTime;
    trackEngagement(analytics, 'time_spent', pageName, timeSpent);
  };

  // Track time spent when component unmounts
  React.useEffect(() => {
    return () => {
      trackTimeSpent();
    };
  }, []);

  return { trackTimeSpent };
}

/**
 * Utility function to track errors with context
 */
export function trackErrorWithContext(
  analytics: any,
  error: Error | string,
  context: {
    component?: string;
    action?: string;
    userId?: string;
    projectId?: string;
    additionalData?: Record<string, any>;
  }
) {
  if (!analytics?.trackError) return;

  const errorMessage = typeof error === 'string' ? error : error.message;
  const stackTrace = typeof error === 'object' ? error.stack : undefined;

  analytics.trackError(MixpanelEventName.errorOccurred, {
    errorMessage,
    stackTrace,
    component: context.component,
    action: context.action,
    userId: context.userId,
    projectId: context.projectId,
    ...context.additionalData,
  });
}

/**
 * Utility function to track performance metrics
 */
export function trackPerformance(
  analytics: any,
  metricName: string,
  value: number,
  unit: 'ms' | 'bytes' | 'count' = 'ms',
  additionalProps?: Record<string, any>
) {
  if (!analytics?.trackEvent) return;

  analytics.trackEvent(MixpanelEventName.pageView, {
    eventType: 'performance',
    metricName,
    value,
    unit,
    ...additionalProps,
  });
}

/**
 * Utility function to track user preferences
 */
export function trackUserPreference(
  analytics: any,
  preferenceName: string,
  value: any,
  previousValue?: any
) {
  if (!analytics?.trackEvent) return;

  analytics.trackEvent(MixpanelEventName.settingsUpdated, {
    preferenceName,
    value,
    previousValue,
    eventType: 'preference_change',
  });
}

/**
 * Utility function to track search queries
 */
export function trackSearch(
  analytics: any,
  query: string,
  resultsCount: number,
  source: string,
  additionalProps?: Record<string, any>
) {
  if (!analytics?.trackEvent) return;

  analytics.trackEvent(MixpanelEventName.pageView, {
    eventType: 'search',
    query,
    resultsCount,
    source,
    ...additionalProps,
  });
}

// Re-export React for the HOCs
import React from 'react';
