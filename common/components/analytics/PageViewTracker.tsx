'use client'

import { useEffect } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import { useAnalytics } from '@/common/hooks/useAnalytics';

/**
 * Component that automatically tracks page views when the route changes
 * Should be placed in layouts or pages where you want to track navigation
 */
export function PageViewTracker() {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { trackPageView, isEnabled } = useAnalytics();

  useEffect(() => {
    if (!isEnabled) return;

    // Track page view with additional context
    const search = searchParams.toString();
    const fullPath = search ? `${pathname}?${search}` : pathname;
    
    // Add page-specific properties
    const pageProps = {
      path: pathname,
      search: search || undefined,
      fullPath,
      // Add page type based on pathname
      pageType: getPageType(pathname),
      // Add section based on pathname
      section: getSection(pathname),
    };

    trackPageView(pathname, pageProps);
  }, [pathname, searchParams, trackPageView, isEnabled]);

  return null; // This component doesn't render anything
}

/**
 * Determines the page type based on the pathname
 */
function getPageType(pathname: string): string {
  if (pathname === '/') return 'landing';
  if (pathname.startsWith('/dashboard')) return 'dashboard';
  if (pathname.startsWith('/payment')) return 'payment';
  if (pathname === '/privacy') return 'legal';
  if (pathname === '/terms') return 'legal';
  return 'other';
}

/**
 * Determines the section based on the pathname
 */
function getSection(pathname: string): string {
  if (pathname === '/') return 'home';
  if (pathname === '/dashboard') return 'overview';
  if (pathname === '/dashboard/analytics') return 'analytics';
  if (pathname === '/dashboard/calendar') return 'calendar';
  if (pathname === '/dashboard/calendar/schedule') return 'schedule';
  if (pathname === '/dashboard/accounts') return 'accounts';
  if (pathname === '/dashboard/knowledge-base') return 'knowledge';
  if (pathname === '/dashboard/settings') return 'settings';
  if (pathname.startsWith('/payment')) return 'billing';
  return 'other';
}

/**
 * Hook for manual page view tracking with custom properties
 */
export function usePageViewTracking() {
  const { trackPageView } = useAnalytics();

  const trackCustomPageView = (pageName: string, additionalProps?: Record<string, any>) => {
    trackPageView(pageName, {
      customPage: true,
      pageName,
      ...additionalProps,
    });
  };

  return { trackCustomPageView };
}
