'use client'

import {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  ReactNode,
} from 'react';
import { createClient } from '@/common/utils/supabase/client';
import { Project } from '@/common/types/supabase';
import { SupabaseTables } from '@/common/constants';
import { useSupabaseAuth } from '@/common/hooks/useSupabaseAuth';
import { checkTwitterPremiumUrl } from '@/common/utils/network/endpoints';
import { useAnalytics } from '@/common/hooks/useAnalytics';

interface ProjectContextType {
  projects: Project[];
  activeProject: Project | null;
  isLoading: boolean;
  error: Error | null;
  fetchProjects: () => Promise<void>;
  setActiveProject: (project: Project) => void;
  updateTwitterPremiumStatus: (projectId: string) => Promise<void>;
}

const ProjectContext = createContext<ProjectContextType | undefined>(undefined);

export function ProjectProvider ({
  children,
  initialFetch = true,
}: {
  children: ReactNode;
  initialFetch?: boolean;
}) {
  const [projects, setProjects] = useState<Project[]>([]);
  const [activeProject, setActiveProject] = useState<Project | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(initialFetch);
  const [error, setError] = useState<Error | null>(null);
  const [hasInitiallyFetched, setHasInitiallyFetched] = useState<boolean>(false);

  const { user } = useSupabaseAuth();
  const supabase = createClient();
  const analytics = useAnalytics();

  const fetchProjects = useCallback(async (force = false) => {
    if (hasInitiallyFetched && !force && projects.length > 0) {
      return;
    }

    if (!user) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const { 
        data, error: supabaseError,
      } = await supabase
        .from(SupabaseTables.Projects)
        .select('*')
        .eq('user_id', user.id);

      if (supabaseError) {
        throw new Error(`Error fetching projects: ${supabaseError.message}`);
      }

      if (data) {
        const processedProjects = data.map((project) => ({
          ...project,
          accounts: project.accounts ? JSON.parse(project.accounts).accounts : [],
        }));

        setProjects(processedProjects);

        if (processedProjects.length > 0 && !activeProject) {
          setActiveProject(processedProjects[0]);

          // Track initial project selection
          analytics.trackProjectSelected(
            processedProjects[0].project_id,
            processedProjects[0].name,
            {
              projectId: processedProjects[0].project_id,
              billingStatus: processedProjects[0].billing_status,
              accountsCount: processedProjects[0].accounts?.length || 0,
              isAutoSelected: true,
            }
          );
        }

        setHasInitiallyFetched(true);
      }
    } catch (err) {
      console.error('Error fetching projects:', err);
      setError(err instanceof Error ? err : new Error('Unknown error occurred while fetching projects'));
    } finally {
      setIsLoading(false);
    }
  }, [user, supabase, activeProject, hasInitiallyFetched, projects.length]);

  const updateTwitterPremiumStatus = useCallback(async (projectId: string) => {
    try {
      const project = projects.find(p => p.project_id === projectId);
      if (!project) {
        return;
      }

      const twitterAccount = project.accounts.find(acc =>
        (acc.platform.toLowerCase() === 'twitter' || acc.platform.toLowerCase() === 'x') &&
        acc.connected && acc.agentId,
      );

      if (!twitterAccount?.agentId) {
        return;
      }

      const response = await fetch(checkTwitterPremiumUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          agentId: twitterAccount.agentId,
        }),
      });

      if (!response.ok) {
        console.error('Failed to check Twitter premium status');
        return;
      }

      const data = await response.json();
      if (data.success && data.data) {
        const premiumData = data.data;

        const updatedAccounts = project.accounts.map(acc => {
          if (acc.agentId === twitterAccount.agentId) {
            const updatedAccount = {
              ...acc,
              hasPremium: premiumData.hasPremium,
              subscriptionType: premiumData.subscriptionType,
              verifiedType: premiumData.verifiedType,
              isBlueVerified: premiumData.isBlueVerified,
            };

            // Track Twitter premium upgrade if status changed
            if (!acc.hasPremium && premiumData.hasPremium) {
              analytics.trackTwitterPremiumUpgrade(acc.username, {
                projectId: project.project_id,
                platform: 'twitter',
                subscriptionType: premiumData.subscriptionType,
                verifiedType: premiumData.verifiedType,
                isBlueVerified: premiumData.isBlueVerified,
              });
            }

            return updatedAccount;
          }
          return acc;
        });

        const accountsData = {
          accounts: updatedAccounts,
        };
        await supabase
          .from(SupabaseTables.Projects)
          .update({
            accounts: JSON.stringify(accountsData),
          })
          .eq('project_id', projectId);

        setProjects(prevProjects =>
          prevProjects.map(p =>
            p.project_id === projectId
              ? {
                ...p,
                accounts: updatedAccounts,
              }
              : p,
          ),
        );

        if (activeProject?.project_id === projectId) {
          setActiveProject({
            ...activeProject,
            accounts: updatedAccounts,
          });
        }
      }
    } catch (error) {
      console.error('Error updating Twitter premium status:', error);
    }
  }, [projects, supabase, activeProject]);

  // Wrapper for setActiveProject to include analytics tracking
  const setActiveProjectWithTracking = useCallback((project: Project) => {
    setActiveProject(project);

    // Track manual project selection
    analytics.trackProjectSelected(
      project.project_id,
      project.name,
      {
        projectId: project.project_id,
        billingStatus: project.billing_status,
        accountsCount: project.accounts?.length || 0,
        isAutoSelected: false,
      }
    );
  }, [analytics]);

  useEffect(() => {
    if (initialFetch && !hasInitiallyFetched) {
      fetchProjects();
    }
  }, [fetchProjects, initialFetch, hasInitiallyFetched]);

  const value = {
    projects,
    activeProject,
    isLoading,
    error,
    fetchProjects,
    setActiveProject: setActiveProjectWithTracking,
    updateTwitterPremiumStatus,
  };

  return (
    <ProjectContext.Provider value={value}>
      {children}
    </ProjectContext.Provider>
  );
}

export function useProjectContext () {
  const context = useContext(ProjectContext);
  if (context === undefined) {
    throw new Error('useProjectContext must be used within a ProjectProvider');
  }
  return context;
}
