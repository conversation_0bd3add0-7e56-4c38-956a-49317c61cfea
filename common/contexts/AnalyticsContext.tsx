'use client'

import {
  createContext,
  useContext,
  useEffect,
  useCallback,
  ReactNode,
} from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import { User } from '@supabase/supabase-js';
import { Mixpanel } from '@/common/utils/mixpanel';
import {
  MixpanelEventName,
  BaseEventProps,
  AuthEventProps,
  ProjectEventProps,
  AccountEventProps,
  ContentEventProps,
  ErrorEventProps,
} from '@/common/utils/mixpanel/types';
import { mixpanelToken } from '@/common/constants';

interface AnalyticsContextType {
  // User identification
  identifyUser: (user: User) => void;
  clearUser: () => void;
  
  // Event tracking
  trackPageView: (path: string, additionalProps?: BaseEventProps) => void;
  trackAuth: (eventName: MixpanelEventName, props?: AuthEventProps) => void;
  trackProject: (eventName: MixpanelEventName, props?: ProjectEventProps) => void;
  trackAccount: (eventName: MixpanelEventName, props?: AccountEventProps) => void;
  trackContent: (eventName: MixpanelEventName, props?: ContentEventProps) => void;
  trackError: (eventName: MixpanelEventName, props?: ErrorEventProps) => void;
  trackEvent: (eventName: MixpanelEventName, props?: BaseEventProps) => void;
  
  // User properties
  setUserProperties: (properties: Record<string, any>) => void;
  
  // Utility
  isEnabled: boolean;
}

const AnalyticsContext = createContext<AnalyticsContextType | undefined>(undefined);

interface AnalyticsProviderProps {
  children: ReactNode;
  user?: User | null;
}

export function AnalyticsProvider({ children, user }: AnalyticsProviderProps) {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const isEnabled = !!mixpanelToken;

  // Get common properties for all events
  const getCommonProps = useCallback((): BaseEventProps => {
    return {
      userId: user?.id,
      timestamp: Date.now(),
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : undefined,
      url: typeof window !== 'undefined' ? window.location.href : undefined,
      referrer: typeof window !== 'undefined' ? document.referrer : undefined,
    };
  }, [user?.id]);

  // User identification
  const identifyUser = useCallback((user: User) => {
    if (!isEnabled) return;
    
    try {
      Mixpanel.identify(user.id);
      
      // Set user properties
      const userProperties = {
        $email: user.email,
        $name: user.user_metadata?.name || user.email?.split('@')[0],
        $created: user.created_at,
        $last_login: new Date().toISOString(),
        email_verified: user.email_confirmed_at ? true : false,
        provider: user.app_metadata?.provider,
      };
      
      Mixpanel.people.set(userProperties);
      
      // Register super properties that will be sent with every event
      Mixpanel.register({
        userId: user.id,
        userEmail: user.email,
        userProvider: user.app_metadata?.provider,
      });
    } catch (error) {
      console.error('Analytics: Error identifying user:', error);
    }
  }, [isEnabled]);

  const clearUser = useCallback(() => {
    if (!isEnabled) return;
    
    try {
      // Note: Mixpanel doesn't have a built-in clear user method
      // We'll just register empty super properties
      Mixpanel.register({
        userId: undefined,
        userEmail: undefined,
        userProvider: undefined,
      });
    } catch (error) {
      console.error('Analytics: Error clearing user:', error);
    }
  }, [isEnabled]);

  // Page view tracking
  const trackPageView = useCallback((path: string, additionalProps?: BaseEventProps) => {
    if (!isEnabled) return;
    
    try {
      const props = {
        ...getCommonProps(),
        ...additionalProps,
        path,
        search: searchParams.toString(),
      };
      
      Mixpanel.track(MixpanelEventName.pageView, props);
    } catch (error) {
      console.error('Analytics: Error tracking page view:', error);
    }
  }, [isEnabled, getCommonProps, searchParams]);

  // Specific event tracking methods
  const trackAuth = useCallback((eventName: MixpanelEventName, props?: AuthEventProps) => {
    if (!isEnabled) return;
    
    try {
      const eventProps = {
        ...getCommonProps(),
        ...props,
      };
      
      Mixpanel.track(eventName, eventProps);
    } catch (error) {
      console.error('Analytics: Error tracking auth event:', error);
    }
  }, [isEnabled, getCommonProps]);

  const trackProject = useCallback((eventName: MixpanelEventName, props?: ProjectEventProps) => {
    if (!isEnabled) return;
    
    try {
      const eventProps = {
        ...getCommonProps(),
        ...props,
      };
      
      Mixpanel.track(eventName, eventProps);
    } catch (error) {
      console.error('Analytics: Error tracking project event:', error);
    }
  }, [isEnabled, getCommonProps]);

  const trackAccount = useCallback((eventName: MixpanelEventName, props?: AccountEventProps) => {
    if (!isEnabled) return;
    
    try {
      const eventProps = {
        ...getCommonProps(),
        ...props,
      };
      
      Mixpanel.track(eventName, eventProps);
    } catch (error) {
      console.error('Analytics: Error tracking account event:', error);
    }
  }, [isEnabled, getCommonProps]);

  const trackContent = useCallback((eventName: MixpanelEventName, props?: ContentEventProps) => {
    if (!isEnabled) return;
    
    try {
      const eventProps = {
        ...getCommonProps(),
        ...props,
      };
      
      Mixpanel.track(eventName, eventProps);
    } catch (error) {
      console.error('Analytics: Error tracking content event:', error);
    }
  }, [isEnabled, getCommonProps]);

  const trackError = useCallback((eventName: MixpanelEventName, props?: ErrorEventProps) => {
    if (!isEnabled) return;
    
    try {
      const eventProps = {
        ...getCommonProps(),
        ...props,
      };
      
      Mixpanel.track(eventName, eventProps);
    } catch (error) {
      console.error('Analytics: Error tracking error event:', error);
    }
  }, [isEnabled, getCommonProps]);

  const trackEvent = useCallback((eventName: MixpanelEventName, props?: BaseEventProps) => {
    if (!isEnabled) return;
    
    try {
      const eventProps = {
        ...getCommonProps(),
        ...props,
      };
      
      Mixpanel.track(eventName, eventProps);
    } catch (error) {
      console.error('Analytics: Error tracking event:', error);
    }
  }, [isEnabled, getCommonProps]);

  const setUserProperties = useCallback((properties: Record<string, any>) => {
    if (!isEnabled) return;
    
    try {
      Mixpanel.people.set(properties);
    } catch (error) {
      console.error('Analytics: Error setting user properties:', error);
    }
  }, [isEnabled]);

  // Auto-identify user when user changes
  useEffect(() => {
    if (user) {
      identifyUser(user);
    } else {
      clearUser();
    }
  }, [user, identifyUser, clearUser]);

  // Auto-track page views when pathname changes
  useEffect(() => {
    trackPageView(pathname);
  }, [pathname, trackPageView]);

  const value: AnalyticsContextType = {
    identifyUser,
    clearUser,
    trackPageView,
    trackAuth,
    trackProject,
    trackAccount,
    trackContent,
    trackError,
    trackEvent,
    setUserProperties,
    isEnabled,
  };

  return (
    <AnalyticsContext.Provider value={value}>
      {children}
    </AnalyticsContext.Provider>
  );
}

export function useAnalytics(): AnalyticsContextType {
  const context = useContext(AnalyticsContext);
  if (context === undefined) {
    throw new Error('useAnalytics must be used within an AnalyticsProvider');
  }
  return context;
}
