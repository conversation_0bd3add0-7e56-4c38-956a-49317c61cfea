'use client'

import { useCallback } from 'react';
import { useAnalytics as useAnalyticsContext } from '@/common/contexts/AnalyticsContext';
import {
  MixpanelEventName,
  AuthEventProps,
  ProjectEventProps,
  AccountEventProps,
  ContentEventProps,
  ErrorEventProps,
} from '@/common/utils/mixpanel/types';

/**
 * Custom hook that provides convenient analytics tracking methods
 * with pre-configured event names and common patterns
 */
export function useAnalytics() {
  const analytics = useAnalyticsContext();

  // Authentication tracking
  const trackSignUp = useCallback((props?: Omit<AuthEventProps, 'isNewUser'>) => {
    analytics.trackAuth(MixpanelEventName.signUp, { ...props, isNewUser: true });
  }, [analytics]);

  const trackSignIn = useCallback((props?: Omit<AuthEventProps, 'isNewUser'>) => {
    analytics.trackAuth(MixpanelEventName.signIn, { ...props, isNewUser: false });
  }, [analytics]);

  const trackSignOut = useCallback((props?: AuthEventProps) => {
    analytics.trackAuth(MixpanelEventName.signOut, props);
  }, [analytics]);

  const trackAuthError = useCallback((error: string, props?: AuthEventProps) => {
    analytics.trackAuth(MixpanelEventName.authError, { 
      ...props, 
      errorMessage: error 
    });
  }, [analytics]);

  // Project tracking
  const trackProjectCreated = useCallback((projectName: string, props?: ProjectEventProps) => {
    analytics.trackProject(MixpanelEventName.projectCreated, { 
      ...props, 
      projectName 
    });
  }, [analytics]);

  const trackProjectSelected = useCallback((projectId: string, projectName?: string, props?: ProjectEventProps) => {
    analytics.trackProject(MixpanelEventName.projectSelected, { 
      ...props, 
      projectId,
      projectName 
    });
  }, [analytics]);

  // Account connection tracking
  const trackAccountConnected = useCallback((platform: string, username?: string, props?: AccountEventProps) => {
    analytics.trackAccount(MixpanelEventName.accountConnected, { 
      ...props, 
      platform,
      username 
    });
  }, [analytics]);

  const trackAccountDisconnected = useCallback((platform: string, props?: AccountEventProps) => {
    analytics.trackAccount(MixpanelEventName.accountDisconnected, { 
      ...props, 
      platform 
    });
  }, [analytics]);

  const trackAccountConnectionError = useCallback((platform: string, error: string, props?: AccountEventProps) => {
    analytics.trackAccount(MixpanelEventName.accountConnectionError, { 
      ...props, 
      platform,
      errorMessage: error 
    });
  }, [analytics]);

  const trackTwitterPremiumUpgrade = useCallback((username?: string, props?: AccountEventProps) => {
    analytics.trackAccount(MixpanelEventName.twitterPremiumUpgrade, { 
      ...props, 
      platform: 'twitter',
      username 
    });
  }, [analytics]);

  // Content tracking
  const trackPostCreated = useCallback((platforms: string[], props?: ContentEventProps) => {
    analytics.trackContent(MixpanelEventName.postCreated, { 
      ...props, 
      platforms 
    });
  }, [analytics]);

  const trackPostScheduled = useCallback((platforms: string[], scheduledTime: string, props?: ContentEventProps) => {
    analytics.trackContent(MixpanelEventName.postScheduled, { 
      ...props, 
      platforms,
      scheduledTime 
    });
  }, [analytics]);

  const trackContentGenerated = useCallback((platforms: string[], isAI: boolean = true, props?: ContentEventProps) => {
    analytics.trackContent(MixpanelEventName.contentGenerated, { 
      ...props, 
      platforms,
      isAIGenerated: isAI 
    });
  }, [analytics]);

  const trackImageGenerated = useCallback((props?: ContentEventProps) => {
    analytics.trackContent(MixpanelEventName.imageGenerated, { 
      ...props, 
      contentType: 'image',
      isAIGenerated: true 
    });
  }, [analytics]);

  // Bot tracking
  const trackBotCreated = useCallback((platform: string, props?: AccountEventProps) => {
    analytics.trackAccount(MixpanelEventName.botCreated, { 
      ...props, 
      platform 
    });
  }, [analytics]);

  const trackBotStarted = useCallback((platform: string, props?: AccountEventProps) => {
    analytics.trackAccount(MixpanelEventName.botStarted, { 
      ...props, 
      platform 
    });
  }, [analytics]);

  const trackBotStopped = useCallback((platform: string, props?: AccountEventProps) => {
    analytics.trackAccount(MixpanelEventName.botStopped, { 
      ...props, 
      platform 
    });
  }, [analytics]);

  // Navigation tracking
  const trackAnalyticsViewed = useCallback((projectId?: string) => {
    analytics.trackEvent(MixpanelEventName.analyticsViewed, { projectId });
  }, [analytics]);

  const trackCalendarViewed = useCallback((projectId?: string) => {
    analytics.trackEvent(MixpanelEventName.calendarViewed, { projectId });
  }, [analytics]);

  // Error tracking
  const trackError = useCallback((error: Error | string, component?: string, props?: ErrorEventProps) => {
    const errorMessage = typeof error === 'string' ? error : error.message;
    const stackTrace = typeof error === 'object' ? error.stack : undefined;
    
    analytics.trackError(MixpanelEventName.errorOccurred, { 
      ...props, 
      errorMessage,
      stackTrace,
      component 
    });
  }, [analytics]);

  const trackApiError = useCallback((endpoint: string, statusCode: number, error: string, props?: ErrorEventProps) => {
    analytics.trackError(MixpanelEventName.apiError, { 
      ...props, 
      errorMessage: error,
      errorCode: statusCode.toString(),
      component: endpoint 
    });
  }, [analytics]);

  // Button click tracking helper
  const trackButtonClick = useCallback((buttonName: string, location: string, additionalProps?: Record<string, any>) => {
    analytics.trackEvent(MixpanelEventName.pageView, {
      ...additionalProps,
      eventType: 'button_click',
      buttonName,
      location,
    });
  }, [analytics]);

  // Form submission tracking helper
  const trackFormSubmission = useCallback((formName: string, success: boolean, additionalProps?: Record<string, any>) => {
    analytics.trackEvent(MixpanelEventName.pageView, {
      ...additionalProps,
      eventType: 'form_submission',
      formName,
      success,
    });
  }, [analytics]);

  return {
    // Core analytics methods
    ...analytics,
    
    // Convenience methods for common events
    trackSignUp,
    trackSignIn,
    trackSignOut,
    trackAuthError,
    trackProjectCreated,
    trackProjectSelected,
    trackAccountConnected,
    trackAccountDisconnected,
    trackAccountConnectionError,
    trackTwitterPremiumUpgrade,
    trackPostCreated,
    trackPostScheduled,
    trackContentGenerated,
    trackImageGenerated,
    trackBotCreated,
    trackBotStarted,
    trackBotStopped,
    trackAnalyticsViewed,
    trackCalendarViewed,
    trackError,
    trackApiError,
    trackButtonClick,
    trackFormSubmission,
  };
}
