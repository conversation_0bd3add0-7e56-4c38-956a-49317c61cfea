# Cache Configs
CACHE_STORE=database # Defaults to database. Other available cache store: redis and filesystem
REDIS_URL= # Redis URL - could be a local redis instance or cloud hosted redis. Also support rediss:// urls

# Discord Configuration
DISCORD_APPLICATION_ID=
DISCORD_API_TOKEN=              # Bot token
DISCORD_VOICE_CHANNEL_ID=       # The ID of the voice channel the bot should join (optional)

# AI Model API Keys
OPENAI_API_KEY=                 # OpenAI API key, starting with sk-
OPENAI_API_URL=                 # OpenAI API Endpoint (optional), Default: https://api.openai.com/v1
SMALL_OPENAI_MODEL=             # Default: gpt-4o-mini
MEDIUM_OPENAI_MODEL=            # Default: gpt-4o
LARGE_OPENAI_MODEL=             # Default: gpt-4o
EMBEDDING_OPENAI_MODEL=         # Default: text-embedding-3-small
IMAGE_OPENAI_MODEL=             # Default: dall-e-3

# Eternal AI's Decentralized Inference API
ETERNALAI_URL=
ETERNALAI_MODEL=                # Default: "neuralmagic/Meta-Llama-3.1-405B-Instruct-quantized.w4a16"
ETERNALAI_API_KEY=
PASSWORD_PUBLIC_KEY=
GROK_API_KEY=                   # GROK API Key
GROQ_API_KEY=                   # Starts with gsk_
OPENROUTER_API_KEY=
GOOGLE_GENERATIVE_AI_API_KEY=   # Gemini API key

ALI_BAILIAN_API_KEY=            # Ali Bailian API Key
NANOGPT_API_KEY=                # NanoGPT API Key

HYPERBOLIC_API_KEY=             # Hyperbolic API Key
HYPERBOLIC_MODEL=
IMAGE_HYPERBOLIC_MODEL=         # Default: FLUX.1-dev
SMALL_HYPERBOLIC_MODEL=         # Default: meta-llama/Llama-3.2-3B-Instruct
MEDIUM_HYPERBOLIC_MODEL=        # Default: meta-llama/Meta-Llama-3.1-70B-Instruct
LARGE_HYPERBOLIC_MODEL=         # Default: meta-llama/Meta-Llama-3.1-405-Instruct

# Livepeer configuration
LIVEPEER_GATEWAY_URL=           # Free inference gateways and docs: https://livepeer-eliza.com/
LIVEPEER_IMAGE_MODEL=           # Default: ByteDance/SDXL-Lightning

# Speech Synthesis
ELEVENLABS_XI_API_KEY=          # API key from elevenlabs

# Direct Client Setting
EXPRESS_MAX_PAYLOAD= # Default: 100kb

# ElevenLabs Settings
ELEVENLABS_MODEL_ID=eleven_multilingual_v2
ELEVENLABS_VOICE_ID=21m00Tcm4TlvDq8ikWAM
ELEVENLABS_VOICE_STABILITY=0.5
ELEVENLABS_VOICE_SIMILARITY_BOOST=0.9
ELEVENLABS_VOICE_STYLE=0.66
ELEVENLABS_VOICE_USE_SPEAKER_BOOST=false
ELEVENLABS_OPTIMIZE_STREAMING_LATENCY=4
ELEVENLABS_OUTPUT_FORMAT=pcm_16000

# Twitter/X Configuration
TWITTER_DRY_RUN=false
TWITTER_USERNAME=               # Account username
TWITTER_PASSWORD=               # Account password
TWITTER_EMAIL=                  # Account email
TWITTER_2FA_SECRET=

TWITTER_POLL_INTERVAL=120       # How often (in seconds) the bot should check for interactions
TWITTER_SEARCH_ENABLE=FALSE     # Enable timeline search, WARNING this greatly increases your chance of getting banned
TWITTER_TARGET_USERS=           # Comma separated list of Twitter user names to interact with
TWITTER_RETRY_LIMIT=            # Maximum retry attempts for Twitter login

X_SERVER_URL=
XAI_API_KEY=
XAI_MODEL=

# Post Interval Settings (in minutes)
POST_INTERVAL_MIN=              # Default: 90
POST_INTERVAL_MAX=              # Default: 180
POST_IMMEDIATELY=

# Twitter action processing configuration
ACTION_INTERVAL=300000      # Interval in milliseconds between action processing runs (default: 5 minutes)
ENABLE_ACTION_PROCESSING=false   # Set to true to enable the action processing loop

# Feature Flags
IMAGE_GEN=                      # Set to TRUE to enable image generation
USE_OPENAI_EMBEDDING=           # Set to TRUE for OpenAI/1536, leave blank for local
USE_OLLAMA_EMBEDDING=           # Set to TRUE for OLLAMA/1024, leave blank for local

# OpenRouter Models
OPENROUTER_MODEL=               # Default: uses hermes 70b/405b
SMALL_OPENROUTER_MODEL=
MEDIUM_OPENROUTER_MODEL=
LARGE_OPENROUTER_MODEL=

# REDPILL Configuration
# https://docs.red-pill.ai/get-started/supported-models
REDPILL_API_KEY=                # REDPILL API Key
REDPILL_MODEL=
SMALL_REDPILL_MODEL=            # Default: gpt-4o-mini
MEDIUM_REDPILL_MODEL=           # Default: gpt-4o
LARGE_REDPILL_MODEL=            # Default: gpt-4o

# Grok Configuration
SMALL_GROK_MODEL=       # Default: grok-2-1212
MEDIUM_GROK_MODEL=      # Default: grok-2-1212
LARGE_GROK_MODEL=       # Default: grok-2-1212
EMBEDDING_GROK_MODEL=   # Default: grok-2-1212

# Ollama Configuration
OLLAMA_SERVER_URL=              # Default: localhost:11434
OLLAMA_MODEL=
OLLAMA_EMBEDDING_MODEL=         # Default: mxbai-embed-large
SMALL_OLLAMA_MODEL=             # Default: llama3.2
MEDIUM_OLLAMA_MODEL=            # Default: hermes3
LARGE_OLLAMA_MODEL=             # Default: hermes3:70b

# Google Configuration
GOOGLE_MODEL=
SMALL_GOOGLE_MODEL=             # Default: gemini-1.5-flash-latest
MEDIUM_GOOGLE_MODEL=            # Default: gemini-1.5-flash-latest
LARGE_GOOGLE_MODEL=             # Default: gemini-1.5-pro-latest
EMBEDDING_GOOGLE_MODEL=         # Default: text-embedding-004

# Groq Configuration
SMALL_GROQ_MODEL=               # Default: llama-3.1-8b-instant
MEDIUM_GROQ_MODEL=              # Default: llama-3.3-70b-versatile
LARGE_GROQ_MODEL=               # Default: llama-3.2-90b-vision-preview
EMBEDDING_GROQ_MODEL=           # Default: llama-3.1-8b-instant

# LlamaLocal Configuration
LLAMALOCAL_PATH=                # Default: "" which is the current directory in plugin-node/dist/ which gets destroyed and recreated on every build

# NanoGPT Configuration
SMALL_NANOGPT_MODEL=            # Default: gpt-4o-mini
MEDIUM_NANOGPT_MODEL=           # Default: gpt-4o
LARGE_NANOGPT_MODEL=            # Default: gpt-4o

# Anthropic Configuration
ANTHROPIC_API_KEY=              # For Claude
SMALL_ANTHROPIC_MODEL=          # Default: claude-3-haiku-20240307
MEDIUM_ANTHROPIC_MODEL=         # Default: claude-3-5-sonnet-20241022
LARGE_ANTHROPIC_MODEL=          # Default: claude-3-5-sonnet-20241022

# Heurist Configuration
HEURIST_API_KEY=                # Get from https://heurist.ai/dev-access
SMALL_HEURIST_MODEL=            # Default: meta-llama/llama-3-70b-instruct
MEDIUM_HEURIST_MODEL=           # Default: meta-llama/llama-3-70b-instruct
LARGE_HEURIST_MODEL=            # Default: meta-llama/llama-3.1-405b-instruct
HEURIST_IMAGE_MODEL=            # Default: PepeXL

# Gaianet Configuration
GAIANET_MODEL=
GAIANET_SERVER_URL=

SMALL_GAIANET_MODEL=            # Default: llama3b
SMALL_GAIANET_SERVER_URL=       # Default: https://llama3b.gaia.domains/v1
MEDIUM_GAIANET_MODEL=           # Default: llama
MEDIUM_GAIANET_SERVER_URL=      # Default: https://llama8b.gaia.domains/v1
LARGE_GAIANET_MODEL=            # Default: qwen72b
LARGE_GAIANET_SERVER_URL=       # Default: https://qwen72b.gaia.domains/v1

GAIANET_EMBEDDING_MODEL=
USE_GAIANET_EMBEDDING=          # Set to TRUE for GAIANET/768, leave blank for local

# EVM
EVM_PRIVATE_KEY=
EVM_PROVIDER_URL=

# Avalanche
AVALANCHE_PRIVATE_KEY=
AVALANCHE_PUBLIC_KEY=

# Solana
SOLANA_PRIVATE_KEY=
SOLANA_PUBLIC_KEY=
SOLANA_CLUSTER= # Default: devnet. Solana Cluster: 'devnet' | 'testnet' | 'mainnet-beta'
SOLANA_ADMIN_PRIVATE_KEY= # This wallet is used to verify NFTs
SOLANA_ADMIN_PUBLIC_KEY= # This wallet is used to verify NFTs
SOLANA_VERIFY_TOKEN= # Authentication token for calling the verification API

# Fallback Wallet Configuration (deprecated)
WALLET_PRIVATE_KEY=
WALLET_PUBLIC_KEY=

BIRDEYE_API_KEY=

# Solana Configuration
SOL_ADDRESS=So11111111111111111111111111111111111111112
SLIPPAGE=1
BASE_MINT=So11111111111111111111111111111111111111112
RPC_URL=https://api.mainnet-beta.solana.com
HELIUS_API_KEY=

# Telegram Configuration
TELEGRAM_BOT_TOKEN=

# Together Configuration
TOGETHER_API_KEY=

# Server Configuration
SERVER_PORT=3000

# Abstract Configuration
ABSTRACT_ADDRESS=
ABSTRACT_PRIVATE_KEY=
ABSTRACT_RPC_URL=https://api.testnet.abs.xyz

# Starknet Configuration
STARKNET_ADDRESS=
STARKNET_PRIVATE_KEY=
STARKNET_RPC_URL=

# Intiface Configuration
INTIFACE_WEBSOCKET_URL=ws://localhost:12345

# Farcaster Neynar Configuration
FARCASTER_FID=                  # The FID associated with the account your are sending casts from
FARCASTER_NEYNAR_API_KEY=       # Neynar API key: https://neynar.com/
FARCASTER_NEYNAR_SIGNER_UUID=   # Signer for the account you are sending casts from. Create a signer here: https://dev.neynar.com/app
FARCASTER_DRY_RUN=false         # Set to true if you want to run the bot without actually publishing casts
FARCASTER_POLL_INTERVAL=120     # How often (in seconds) the bot should check for farcaster interactions (replies and mentions)

# Coinbase
COINBASE_COMMERCE_KEY=          # From Coinbase developer portal
COINBASE_API_KEY=               # From Coinbase developer portal
COINBASE_PRIVATE_KEY=           # From Coinbase developer portal
COINBASE_GENERATED_WALLET_ID=   # Not your address but the wallet ID from generating a wallet through the plugin
COINBASE_GENERATED_WALLET_HEX_SEED= # Not your address but the wallet hex seed from generating a wallet through the plugin and calling export
COINBASE_NOTIFICATION_URI=      # For webhook plugin the uri you want to send the webhook to for dummy ones use https://webhook.site

# Coinbase Charity Configuration
IS_CHARITABLE=false   # Set to true to enable charity donations
CHARITY_ADDRESS_BASE=******************************************
CHARITY_ADDRESS_SOL=pWvDXKu6CpbKKvKQkZvDA66hgsTB6X2AgFxksYogHLV
CHARITY_ADDRESS_ETH=******************************************
CHARITY_ADDRESS_ARB=******************************************
CHARITY_ADDRESS_POL=******************************************

# Conflux Configuration
CONFLUX_CORE_PRIVATE_KEY=
CONFLUX_CORE_SPACE_RPC_URL=
CONFLUX_ESPACE_PRIVATE_KEY=
CONFLUX_ESPACE_RPC_URL=
CONFLUX_MEME_CONTRACT_ADDRESS=

# ZeroG
ZEROG_INDEXER_RPC=
ZEROG_EVM_RPC=
ZEROG_PRIVATE_KEY=
ZEROG_FLOW_ADDRESS=

# TEE Configuration
# TEE_MODE options:
# - LOCAL: Uses simulator at localhost:8090 (for local development)
# - DOCKER: Uses simulator at host.docker.internal:8090 (for docker development)
# - PRODUCTION: No simulator, uses production endpoints
# Defaults to OFF if not specified
TEE_MODE=OFF                    # LOCAL | DOCKER | PRODUCTION
WALLET_SECRET_SALT=             # ONLY define if you want to use TEE Plugin, otherwise it will throw errors

# Galadriel Configuration
GALADRIEL_API_KEY=gal-*         # Get from https://dashboard.galadriel.com/

# Venice Configuration
VENICE_API_KEY=                 # generate from venice settings
SMALL_VENICE_MODEL=             # Default: llama-3.3-70b
MEDIUM_VENICE_MODEL=            # Default: llama-3.3-70b
LARGE_VENICE_MODEL=             # Default: llama-3.1-405b
IMAGE_VENICE_MODEL=             # Default: fluently-xl

# Akash Chat API Configuration docs: https://chatapi.akash.network/documentation
AKASH_CHAT_API_KEY= # Get from https://chatapi.akash.network/
SMALL_AKASH_CHAT_API_MODEL=  # Default: Meta-Llama-3-2-3B-Instruct
MEDIUM_AKASH_CHAT_API_MODEL= # Default: Meta-Llama-3-3-70B-Instruct
LARGE_AKASH_CHAT_API_MODEL=  # Default: Meta-Llama-3-1-405B-Instruct-FP8

# fal.ai Configuration
FAL_API_KEY=
FAL_AI_LORA_PATH=

# Web search API Configuration
TAVILY_API_KEY=

# WhatsApp Cloud API Configuration
WHATSAPP_ACCESS_TOKEN=          # Permanent access token from Facebook Developer Console
WHATSAPP_PHONE_NUMBER_ID=       # Phone number ID from WhatsApp Business API
WHATSAPP_BUSINESS_ACCOUNT_ID=   # Business Account ID from Facebook Business Manager
WHATSAPP_WEBHOOK_VERIFY_TOKEN=  # Custom string for webhook verification
WHATSAPP_API_VERSION=v17.0      # WhatsApp API version (default: v17.0)

# Flow Blockchain Configuration
FLOW_ADDRESS=
FLOW_PRIVATE_KEY=               # Private key for SHA3-256 + P256 ECDSA
FLOW_NETWORK=                   # Default: mainnet
FLOW_ENDPOINT_URL=              # Default: https://mainnet.onflow.org

# ICP
INTERNET_COMPUTER_PRIVATE_KEY=
INTERNET_COMPUTER_ADDRESS=

# Aptos
APTOS_PRIVATE_KEY=              # Aptos private key
APTOS_NETWORK=                  # Must be one of mainnet, testnet

# EchoChambers Configuration
ECHOCHAMBERS_API_URL=http://127.0.0.1:3333
ECHOCHAMBERS_API_KEY=testingkey0011
ECHOCHAMBERS_USERNAME=eliza
ECHOCHAMBERS_DEFAULT_ROOM=general
ECHOCHAMBERS_POLL_INTERVAL=60
ECHOCHAMBERS_MAX_MESSAGES=10

# MultiversX
MVX_PRIVATE_KEY= # Multiversx private key
MVX_NETWORK= # must be one of mainnet, devnet, testnet

# NEAR
NEAR_WALLET_SECRET_KEY=
NEAR_WALLET_PUBLIC_KEY=
NEAR_ADDRESS=
SLIPPAGE=1
RPC_URL=https://rpc.testnet.near.org
NEAR_NETWORK=testnet # or mainnet

# ZKsync Era Configuration
ZKSYNC_ADDRESS=
ZKSYNC_PRIVATE_KEY=

# Ton
TON_PRIVATE_KEY= # Ton Mnemonic Seed Phrase Join With Empty String
TON_RPC_URL=     # ton rpc

# AWS S3 Configuration Settings for File Upload
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=
AWS_S3_BUCKET=
AWS_S3_UPLOAD_PATH=

# Deepgram
DEEPGRAM_API_KEY=

# Sui
SUI_PRIVATE_KEY= # Sui Mnemonic Seed Phrase (`sui keytool generate ed25519`)
SUI_NETWORK=     # must be one of mainnet, testnet, devnet, localnet

# Story
STORY_PRIVATE_KEY= # Story private key
STORY_API_BASE_URL= # Story API base URL
STORY_API_KEY= # Story API key
PINATA_JWT= # Pinata JWT for uploading files to IPFS

# Cronos zkEVM
CRONOSZKEVM_ADDRESS=
CRONOSZKEVM_PRIVATE_KEY=

# Fuel Ecosystem (FuelVM)
FUEL_WALLET_PRIVATE_KEY=
