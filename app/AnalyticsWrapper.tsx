'use client'

import { ReactNode, useEffect, useState } from 'react';
import { User } from '@supabase/supabase-js';
import { createClient } from '@/common/utils/supabase/client';
import { AnalyticsProvider } from '@/common/contexts/AnalyticsContext';

interface AnalyticsWrapperProps {
  children: ReactNode;
}

export function AnalyticsWrapper({ children }: AnalyticsWrapperProps) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const supabase = createClient();

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        setUser(session?.user || null);
      } catch (error) {
        console.error('Error getting initial session:', error);
      } finally {
        setIsLoading(false);
      }
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setUser(session?.user || null);
        setIsLoading(false);
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [supabase.auth]);

  // Don't render analytics provider until we know the auth state
  if (isLoading) {
    return <>{children}</>;
  }

  return (
    <AnalyticsProvider user={user}>
      {children}
    </AnalyticsProvider>
  );
}
