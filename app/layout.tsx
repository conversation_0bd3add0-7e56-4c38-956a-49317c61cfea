import {
  ReactNode,
} from "react";
import {
  primaryFont,
} from "@/common/utils/localFont";
import { metaObject } from "./metaData";
import './globals.css'
import { AnalyticsWrapper } from "./AnalyticsWrapper";

export const metadata = metaObject

export default async function RootLayout ({
  children,
}: {
  children: ReactNode;
}) {
  return (
    <html lang="en" className={`${primaryFont.className}`}>
      <body className={`bg-eerie-black`}>
        <AnalyticsWrapper>
          {children}
        </AnalyticsWrapper>
      </body>
    </html>
  )
}
