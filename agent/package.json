{"name": "@elizaos/agent", "version": "0.1.7-alpha.2", "main": "src/index.ts", "type": "module", "scripts": {"start": "node --loader ts-node/esm src/index.ts", "dev": "node --loader ts-node/esm src/index.ts", "check-types": "tsc --noEmit", "test": "jest"}, "nodemonConfig": {"watch": ["src", "../core/dist"], "ext": "ts,json", "exec": "node --enable-source-maps --loader ts-node/esm src/index.ts"}, "dependencies": {"@elizaos/adapter-postgres": "workspace:*", "@elizaos/adapter-redis": "workspace:*", "@elizaos/adapter-sqlite": "workspace:*", "@elizaos/client-auto": "workspace:*", "@elizaos/client-direct": "workspace:*", "@elizaos/client-discord": "workspace:*", "@elizaos/client-farcaster": "workspace:*", "@elizaos/client-lens": "workspace:*", "@elizaos/client-telegram": "workspace:*", "@elizaos/client-twitter": "workspace:*", "agent-twitter-client": "workspace:*", "agent-linkedin-client": "workspace:*", "@elizaos/client-slack": "workspace:*", "@elizaos/core": "workspace:*", "@elizaos/plugin-0g": "workspace:*", "@elizaos/plugin-abstract": "workspace:*", "@elizaos/plugin-aptos": "workspace:*", "@elizaos/plugin-bootstrap": "workspace:*", "@elizaos/plugin-intiface": "workspace:*", "@elizaos/plugin-coinbase": "workspace:*", "@elizaos/plugin-conflux": "workspace:*", "@elizaos/plugin-evm": "workspace:*", "@elizaos/plugin-flow": "workspace:*", "@elizaos/plugin-gitbook": "workspace:*", "@elizaos/plugin-story": "workspace:*", "@elizaos/plugin-goat": "workspace:*", "@elizaos/plugin-icp": "workspace:*", "@elizaos/plugin-image-generation": "workspace:*", "@elizaos/plugin-nft-generation": "workspace:*", "@elizaos/plugin-node": "workspace:*", "@elizaos/plugin-solana": "workspace:*", "@elizaos/plugin-starknet": "workspace:*", "@elizaos/plugin-ton": "workspace:*", "@elizaos/plugin-sui": "workspace:*", "@elizaos/plugin-tee": "workspace:*", "@elizaos/plugin-multiversx": "workspace:*", "@elizaos/plugin-near": "workspace:*", "@elizaos/plugin-zksync-era": "workspace:*", "@elizaos/plugin-twitter": "workspace:*", "@elizaos/plugin-cronoszkevm": "workspace:*", "@elizaos/plugin-3d-generation": "workspace:*", "@elizaos/plugin-fuel": "workspace:*", "@elizaos/plugin-avalanche": "workspace:*", "readline": "1.3.0", "ws": "8.18.0", "yargs": "17.7.2"}, "devDependencies": {"@types/jest": "^29.5.14", "jest": "^29.7.0", "ts-jest": "^29.2.5", "ts-node": "10.9.2", "tsup": "8.3.5"}}