import { PostgresDatabaseAdapter } from "@elizaos/adapter-postgres";
import { SqliteDatabaseAdapter } from "@elizaos/adapter-sqlite";
import { AutoClientInterface } from "@elizaos/client-auto";
import { DiscordClientInterface } from "@elizaos/client-discord";
import { FarcasterAgentClient } from "@elizaos/client-farcaster";
import { LensAgentClient } from "@elizaos/client-lens";
import { SlackClientInterface } from "@elizaos/client-slack";
import { TelegramClientInterface } from "@elizaos/client-telegram";
import { TwitterClientInterface } from "@elizaos/client-twitter";
import { LinkedInClientInterface } from "agent-linkedin-client";
import forge from "node-forge";
import {
    AgentRuntime,
    CacheManager,
    Character,
    Clients,
    DbCacheAdapter,
    defaultCharacter,
    elizaLogger,
    FsCacheAdapter,
    IAgentRuntime,
    IDatabaseAdapter,
    IDatabaseCacheAdapter,
    ModelProviderName,
    settings,
    stringToUuid,
    validateCharacterConfig,
    CacheStore,
    Client,
    ICacheManager,
} from "@elizaos/core";
import { RedisClient } from "@elizaos/adapter-redis";
import { zgPlugin } from "@elizaos/plugin-0g";
import { bootstrapPlugin } from "@elizaos/plugin-bootstrap";
import createGoatPlugin from "@elizaos/plugin-goat";
// import { intifacePlugin } from "@elizaos/plugin-intiface";
import { DirectClient } from "@elizaos/client-direct";
import { aptosPlugin } from "@elizaos/plugin-aptos";
import {
    advancedTradePlugin,
    coinbaseCommercePlugin,
    coinbaseMassPaymentsPlugin,
    tokenContractPlugin,
    tradePlugin,
    webhookPlugin,
} from "@elizaos/plugin-coinbase";
import { confluxPlugin } from "@elizaos/plugin-conflux";
import { evmPlugin } from "@elizaos/plugin-evm";
import { storyPlugin } from "@elizaos/plugin-story";
import { flowPlugin } from "@elizaos/plugin-flow";
import { fuelPlugin } from "@elizaos/plugin-fuel";
import { imageGenerationPlugin } from "@elizaos/plugin-image-generation";
import { ThreeDGenerationPlugin } from "@elizaos/plugin-3d-generation";
import { multiversxPlugin } from "@elizaos/plugin-multiversx";
import { nearPlugin } from "@elizaos/plugin-near";
import { nftGenerationPlugin } from "@elizaos/plugin-nft-generation";
import { createNodePlugin } from "@elizaos/plugin-node";
import { solanaPlugin } from "@elizaos/plugin-solana";
import { suiPlugin } from "@elizaos/plugin-sui";
import { TEEMode, teePlugin } from "@elizaos/plugin-tee";
import { tonPlugin } from "@elizaos/plugin-ton";
import { zksyncEraPlugin } from "@elizaos/plugin-zksync-era";
import { cronosZkEVMPlugin } from "@elizaos/plugin-cronoszkevm";
import { abstractPlugin } from "@elizaos/plugin-abstract";
import { avalanchePlugin } from "@elizaos/plugin-avalanche";
import Database from "better-sqlite3";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import yargs from "yargs";
import net from "net";

const __filename = fileURLToPath(import.meta.url); // get the resolved path to the file
const __dirname = path.dirname(__filename); // get the name of the directory

export const wait = (minTime: number = 1000, maxTime: number = 3000) => {
    const waitTime =
        Math.floor(Math.random() * (maxTime - minTime + 1)) + minTime;
    return new Promise((resolve) => setTimeout(resolve, waitTime));
};

const logFetch = async (url: string, options: any) => {
    elizaLogger.debug(`Fetching ${url}`);
    // Disabled to avoid disclosure of sensitive information such as API keys
    // elizaLogger.debug(JSON.stringify(options, null, 2));
    return fetch(url, options);
};

export function parseArguments(): {
    character?: string;
    characters?: string;
} {
    try {
        return yargs(process.argv.slice(3))
            .option("character", {
                type: "string",
                description: "Path to the character JSON file",
            })
            .option("characters", {
                type: "string",
                description:
                    "Comma separated list of paths to character JSON files",
            })
            .parseSync();
    } catch (error) {
        elizaLogger.error("Error parsing arguments:", error);
        return {};
    }
}

function tryLoadFile(filePath: string): string | null {
    try {
        return fs.readFileSync(filePath, "utf8");
    } catch (e) {
        return null;
    }
}

function isAllStrings(arr: unknown[]): boolean {
    return Array.isArray(arr) && arr.every((item) => typeof item === "string");
}

export async function loadCharacters(
    charactersArg: string
): Promise<Character[]> {
    let characterPaths = charactersArg
        ?.split(",")
        .map((filePath) => filePath.trim());
    const loadedCharacters = [];

    if (characterPaths?.length > 0) {
        for (const characterPath of characterPaths) {
            let content = null;
            let resolvedPath = "";

            // Try different path resolutions in order
            const pathsToTry = [
                characterPath, // exact path as specified
                path.resolve(process.cwd(), characterPath), // relative to cwd
                path.resolve(process.cwd(), "agent", characterPath), // Add this
                path.resolve(__dirname, characterPath), // relative to current script
                path.resolve(
                    __dirname,
                    "characters",
                    path.basename(characterPath)
                ), // relative to agent/characters
                path.resolve(
                    __dirname,
                    "../characters",
                    path.basename(characterPath)
                ), // relative to characters dir from agent
                path.resolve(
                    __dirname,
                    "../../characters",
                    path.basename(characterPath)
                ), // relative to project root characters dir
            ];

            elizaLogger.info(
                "Trying paths:",
                pathsToTry.map((p) => ({
                    path: p,
                    exists: fs.existsSync(p),
                }))
            );

            for (const tryPath of pathsToTry) {
                content = tryLoadFile(tryPath);
                if (content !== null) {
                    resolvedPath = tryPath;
                    break;
                }
            }

            if (content === null) {
                elizaLogger.error(
                    `Error loading character from ${characterPath}: File not found in any of the expected locations`
                );
                elizaLogger.error("Tried the following paths:");
                pathsToTry.forEach((p) => elizaLogger.error(` - ${p}`));
                process.exit(1);
            }

            try {
                const character = JSON.parse(content);
                validateCharacterConfig(character);

                // .id isn't really valid
                const characterId = character.id || character.name;
                const characterPrefix = `CHARACTER.${characterId.toUpperCase().replace(/ /g, "_")}.`;

                const characterSettings = Object.entries(process.env)
                    .filter(([key]) => key.startsWith(characterPrefix))
                    .reduce((settings, [key, value]) => {
                        const settingKey = key.slice(characterPrefix.length);
                        return { ...settings, [settingKey]: value };
                    }, {});

                if (Object.keys(characterSettings).length > 0) {
                    character.settings = character.settings || {};
                    character.settings.secrets = {
                        ...characterSettings,
                        ...character.settings.secrets,
                    };
                }

                // Handle plugins
                if (isAllStrings(character.plugins)) {
                    elizaLogger.info("Plugins are: ", character.plugins);
                    const importedPlugins = await Promise.all(
                        character.plugins.map(async (plugin) => {
                            const importedPlugin = await import(plugin);
                            return importedPlugin.default;
                        })
                    );
                    character.plugins = importedPlugins;
                }

                loadedCharacters.push(character);
                elizaLogger.info(
                    `Successfully loaded character from: ${resolvedPath}`
                );
            } catch (e) {
                elizaLogger.error(
                    `Error parsing character from ${resolvedPath}: ${e}`
                );
                process.exit(1);
            }
        }
    }

    if (loadedCharacters.length === 0) {
        elizaLogger.info("No characters found, using default character");
        loadedCharacters.push(defaultCharacter);
    }

    return loadedCharacters;
}

export function getTokenForProvider(
    provider: ModelProviderName,
    character: Character
): string {
    switch (provider) {
        // no key needed for llama_local or gaianet
        case ModelProviderName.LLAMALOCAL:
            return "";
        case ModelProviderName.OLLAMA:
            return "";
        case ModelProviderName.GAIANET:
            return "";
        case ModelProviderName.OPENAI:
            return (
                character.settings?.secrets?.OPENAI_API_KEY ||
                settings.OPENAI_API_KEY
            );
        case ModelProviderName.ETERNALAI:
            return (
                character.settings?.secrets?.ETERNALAI_API_KEY ||
                settings.ETERNALAI_API_KEY
            );
        case ModelProviderName.LLAMACLOUD:
        case ModelProviderName.TOGETHER:
            return (
                character.settings?.secrets?.LLAMACLOUD_API_KEY ||
                settings.LLAMACLOUD_API_KEY ||
                character.settings?.secrets?.TOGETHER_API_KEY ||
                settings.TOGETHER_API_KEY ||
                character.settings?.secrets?.XAI_API_KEY ||
                settings.XAI_API_KEY ||
                character.settings?.secrets?.OPENAI_API_KEY ||
                settings.OPENAI_API_KEY
            );
        case ModelProviderName.CLAUDE_VERTEX:
        case ModelProviderName.ANTHROPIC:
            return (
                character.settings?.secrets?.ANTHROPIC_API_KEY ||
                character.settings?.secrets?.CLAUDE_API_KEY ||
                settings.ANTHROPIC_API_KEY ||
                settings.CLAUDE_API_KEY
            );
        case ModelProviderName.REDPILL:
            return (
                character.settings?.secrets?.REDPILL_API_KEY ||
                settings.REDPILL_API_KEY
            );
        case ModelProviderName.OPENROUTER:
            return (
                character.settings?.secrets?.OPENROUTER ||
                settings.OPENROUTER_API_KEY
            );
        case ModelProviderName.GROK:
            return (
                character.settings?.secrets?.GROK_API_KEY ||
                settings.GROK_API_KEY
            );
        case ModelProviderName.HEURIST:
            return (
                character.settings?.secrets?.HEURIST_API_KEY ||
                settings.HEURIST_API_KEY
            );
        case ModelProviderName.GROQ:
            return (
                character.settings?.secrets?.GROQ_API_KEY ||
                settings.GROQ_API_KEY
            );
        case ModelProviderName.GALADRIEL:
            return (
                character.settings?.secrets?.GALADRIEL_API_KEY ||
                settings.GALADRIEL_API_KEY
            );
        case ModelProviderName.FAL:
            return (
                character.settings?.secrets?.FAL_API_KEY || settings.FAL_API_KEY
            );
        case ModelProviderName.ALI_BAILIAN:
            return (
                character.settings?.secrets?.ALI_BAILIAN_API_KEY ||
                settings.ALI_BAILIAN_API_KEY
            );
        case ModelProviderName.VOLENGINE:
            return (
                character.settings?.secrets?.VOLENGINE_API_KEY ||
                settings.VOLENGINE_API_KEY
            );
        case ModelProviderName.NANOGPT:
            return (
                character.settings?.secrets?.NANOGPT_API_KEY ||
                settings.NANOGPT_API_KEY
            );
        case ModelProviderName.HYPERBOLIC:
            return (
                character.settings?.secrets?.HYPERBOLIC_API_KEY ||
                settings.HYPERBOLIC_API_KEY
            );
        case ModelProviderName.VENICE:
            return (
                character.settings?.secrets?.VENICE_API_KEY ||
                settings.VENICE_API_KEY
            );
        case ModelProviderName.AKASH_CHAT_API:
            return (
                character.settings?.secrets?.AKASH_CHAT_API_KEY ||
                settings.AKASH_CHAT_API_KEY
            );
        case ModelProviderName.GOOGLE:
            return (
                character.settings?.secrets?.GOOGLE_GENERATIVE_AI_API_KEY ||
                settings.GOOGLE_GENERATIVE_AI_API_KEY
            );
        default:
            const errorMessage = `Failed to get token - unsupported model provider: ${provider}`;
            elizaLogger.error(errorMessage);
            throw new Error(errorMessage);
    }
}

function initializeDatabase(dataDir: string) {
    if (process.env.POSTGRES_URL) {
        elizaLogger.info("Initializing PostgreSQL connection...");
        try {
            // Create database adapter with more robust error handling
            const db = new PostgresDatabaseAdapter({
                connectionString: process.env.POSTGRES_URL,
                parseInputs: true,
                // Add more robust connection pool settings
                max: 10, // Reduce from default 20 to avoid overwhelming the database
                idleTimeoutMillis: 30000,
                connectionTimeoutMillis: 10000, // Increase timeout
            });

            // Test the connection but don't wait for init() to complete
            // This allows the application to start even if database initialization has issues
            db.init()
                .then(() => {
                    elizaLogger.success(
                        "Successfully connected to PostgreSQL database"
                    );
                })
                .catch((error) => {
                    elizaLogger.error(
                        "Failed to initialize PostgreSQL database:",
                        error
                    );
                    elizaLogger.warn(
                        "Application will continue running, but some database features may not work correctly"
                    );
                });

            return db;
        } catch (error) {
            elizaLogger.error("Failed to create PostgreSQL adapter:", error);
            elizaLogger.warn("Falling back to SQLite database");

            // Fall back to SQLite if PostgreSQL connection fails
            const filePath =
                process.env.SQLITE_FILE ?? path.resolve(dataDir, "db.sqlite");
            return new SqliteDatabaseAdapter(new Database(filePath));
        }
    } else {
        const filePath =
            process.env.SQLITE_FILE ?? path.resolve(dataDir, "db.sqlite");
        // ":memory:";
        const db = new SqliteDatabaseAdapter(new Database(filePath));
        return db;
    }
}

// also adds plugins from character file into the runtime
export async function initializeClients(
    character: Character,
    runtime: IAgentRuntime,
    runtimeSettings?: any,
    start = false,
    roomSettings?: any
) {
    // each client can only register once
    // and if we want two we can explicitly support it
    const clients: Record<string, any> = {};
    const clientTypes: string[] =
        character.clients?.map((str) => str.toLowerCase()) || [];
    let twitterFailed = false;
    elizaLogger.log("initializeClients", clientTypes, "for", character.name);

    if (clientTypes.includes(Clients.DIRECT)) {
        const autoClient = await AutoClientInterface.start(runtime);
        if (autoClient) clients.auto = autoClient;
    }

    if (clientTypes.includes(Clients.DISCORD)) {
        const discordClient = await DiscordClientInterface.start(runtime);
        if (discordClient) clients.discord = discordClient;
    }

    if (clientTypes.includes(Clients.TELEGRAM)) {
        const telegramClient = await TelegramClientInterface.start(runtime);
        if (telegramClient) clients.telegram = telegramClient;
    }

    if (clientTypes.includes(Clients.TWITTER)) {
        try {
            let twitterAccessToken, twitterAccessSecret, twitterUsername;

            if (
                roomSettings?.TWITTER_ACCESS_TOKEN &&
                roomSettings?.TWITTER_ACCESS_SECRET &&
                roomSettings?.TWITTER_USERNAME
            ) {
                twitterAccessToken = roomSettings.TWITTER_ACCESS_TOKEN;
                twitterAccessSecret = roomSettings.TWITTER_ACCESS_SECRET;
                twitterUsername = roomSettings.TWITTER_USERNAME;
                elizaLogger.log("Found Twitter tokens in room settings");
            } else if (
                character.settings?.secrets?.TWITTER_ACCESS_TOKEN &&
                character.settings?.secrets?.TWITTER_ACCESS_SECRET &&
                character.settings?.secrets?.TWITTER_USERNAME
            ) {
                twitterAccessToken =
                    character.settings.secrets.TWITTER_ACCESS_TOKEN;
                twitterAccessSecret =
                    character.settings.secrets.TWITTER_ACCESS_SECRET;
                twitterUsername = character.settings.secrets.TWITTER_USERNAME;
                elizaLogger.log("Found Twitter tokens in character settings");
            } else if (
                runtimeSettings?.TWITTER_ACCESS_TOKEN &&
                runtimeSettings?.TWITTER_ACCESS_SECRET &&
                runtimeSettings?.TWITTER_USERNAME
            ) {
                twitterAccessToken = runtimeSettings.TWITTER_ACCESS_TOKEN;
                twitterAccessSecret = runtimeSettings.TWITTER_ACCESS_SECRET;
                twitterUsername = runtimeSettings.TWITTER_USERNAME;
                elizaLogger.log("Found Twitter tokens in runtime settings");
            }

            if (twitterAccessToken && twitterAccessSecret && twitterUsername) {
                elizaLogger.log(
                    "Found Twitter tokens for server-side initialization"
                );

                try {
                    // Set the OAuth tokens in the character settings so they can be accessed by the Twitter client
                    if (!character.settings) {
                        character.settings = {};
                    }
                    if (!character.settings.secrets) {
                        character.settings.secrets = {};
                    }

                    character.settings.secrets.TWITTER_ACCESS_TOKEN =
                        twitterAccessToken;
                    character.settings.secrets.TWITTER_ACCESS_SECRET =
                        twitterAccessSecret;
                    character.settings.secrets.TWITTER_USERNAME =
                        twitterUsername;
                    character.settings.secrets.TWITTER_APP_KEY =
                        process.env.TWITTER_APP_KEY || "";
                    character.settings.secrets.TWITTER_APP_SECRET =
                        process.env.TWITTER_APP_SECRET || "";

                    const { loginSuccess, manager: twitterClient }: any =
                        await TwitterClientInterface.startExternal(
                            runtime,
                            "", // email not needed for token auth
                            twitterUsername,
                            "", // password not needed for token auth
                            character?.botOnly || false
                        );

                    if (loginSuccess && twitterClient) {
                        if (
                            twitterClient.client &&
                            twitterClient.client.twitterClient
                        ) {
                            await runtime.cacheManager?.set(
                                `twitter_agent_tokens_${runtime.agentId}`,
                                {
                                    accessToken: twitterAccessToken,
                                    accessSecret: twitterAccessSecret,
                                    username: twitterUsername,
                                },
                                {
                                    expires: Date.now() + 24 * 60 * 60 * 1000, // 24 hours
                                }
                            );
                        }

                        clients.twitter = twitterClient;
                        elizaLogger.log(
                            "Twitter client initialized with tokens (server-side)"
                        );
                    } else {
                        elizaLogger.warn(
                            "Failed to initialize Twitter client with tokens"
                        );
                    }
                } catch (error) {
                    elizaLogger.error(
                        "Error initializing Twitter with tokens:",
                        error
                    );
                }
            } else {
                elizaLogger.warn(
                    "No Twitter tokens found in room settings for server-side initialization"
                );
                elizaLogger.warn(
                    "Twitter client will be available after OAuth authentication"
                );
            }
        } catch (error) {
            elizaLogger.error(`Error initializing Twitter client: ${error}`);
            twitterFailed = true;
        }
        // const { loginSuccess, manager: twitterClient }: any =
        //     await TwitterClientInterface.startExternal(
        //         runtime,
        //         character.settings?.secrets?.TWITTER_EMAIL,
        //         character.settings?.secrets?.TWITTER_USERNAME,
        //         decryptPassword(character.settings?.secrets?.TWITTER_PASSWORD)
        //     );
        // if (!loginSuccess) {
        //     twitterFailed = true;
        // }
        // if (
        //     twitterClient &&
        //     runtimeSettings &&
        //     loginSuccess &&
        //     (runtimeSettings?.followProfiles ||
        //         runtimeSettings?.processionActions ||
        //         runtimeSettings?.schedulingPosts)
        // ) {
        //     const {
        //         followProfiles,
        //         processionActions,
        //         schedulingPosts,
        //         postInterval,
        //         actionInterval,
        //         followInterval,
        //         twitterTargetUsers,
        //     } = runtimeSettings;
        //     // Configure intervals
        //     twitterClient.search.followInterval = followInterval;
        //     twitterClient.post.actionInterval = actionInterval;
        //     twitterClient.post.postInterval = postInterval;
        //     twitterClient.post.twitterTargetUsers = twitterTargetUsers;
        //     twitterClient.interaction.twitterPollInterval =
        //         actionInterval / 1000;

        //     // Handle follow profiles
        //     twitterClient.search.enableFollow = followProfiles;
        //     await twitterClient.search[followProfiles ? "start" : "stop"]();

        //     // Handle action processing
        //     twitterClient.post.enableActionProcessing = processionActions;
        //     await twitterClient.post[
        //         processionActions ? "startProcessingActions" : "stop"
        //     ]();

        //     if (processionActions) {
        //         await twitterClient.interaction.start();
        //     } else {
        //         await twitterClient.interaction.stop();
        //     }
        //     // Handle scheduled posts
        //     twitterClient.post.enableScheduledPosts = schedulingPosts;
        //     await twitterClient.post[
        //         schedulingPosts ? "start" : "stopNewTweets"
        //     ]();
        //     clients.twitter = twitterClient;
        // }
    }

    if (clientTypes.includes(Clients.FARCASTER)) {
        // why is this one different :(
        const farcasterClient = new FarcasterAgentClient(runtime);
        if (farcasterClient) {
            farcasterClient.start();
            clients.farcaster = farcasterClient;
        }
    }
    if (clientTypes.includes("lens")) {
        const lensClient = new LensAgentClient(runtime);
        lensClient.start();
        clients.lens = lensClient;
    }

    if (clientTypes.includes(Clients.LINKEDIN)) {
        try {
            const { manager: linkedinClient } =
                await LinkedInClientInterface.start(runtime.agentId, runtime);
            linkedinClient.post.enableScheduledPosts =
                runtimeSettings.schedulingPosts;
            linkedinClient.post.postInterval = runtimeSettings.postInterval;
            linkedinClient.client.organizations =
                runtimeSettings.userOrganization;

            // Load business account setting from room settings
            if (
                roomSettings &&
                typeof roomSettings.businessAccount === "boolean"
            ) {
                linkedinClient.client.businessAccount =
                    roomSettings.businessAccount;
            }

            linkedinClient.auth.credentials.accessToken =
                runtimeSettings.LINKEDIN_ACCESS_TOKEN;
            linkedinClient.auth.credentials.expiresAt =
                runtimeSettings.LINKEDIN_EXPIRES_IN;
            linkedinClient.auth.credentials.refreshToken =
                runtimeSettings.LINKEDIN_REFRESH_TOKEN;
            linkedinClient.auth.credentials.refreshTokenExpiresAt =
                runtimeSettings.LINKEDIN_REFRESH_EXPIRES_IN;
            if (start) {
                await linkedinClient.client.fetchAndSetProfile();
            }
            if (linkedinClient.client.profile.username) {
                await linkedinClient.post.start();
            }
            if (linkedinClient) clients.linkedin = linkedinClient;
        } catch (error) {
            elizaLogger.error(`Error initializing LinkedIn client: ${error}`);
        }
    }

    elizaLogger.log("client keys", Object.keys(clients));

    // TODO: Add Slack client to the list
    // Initialize clients as an object

    if (clientTypes.includes("slack")) {
        const slackClient = await SlackClientInterface.start(runtime);
        if (slackClient) clients.slack = slackClient; // Use object property instead of push
    }

    function determineClientType(client: Client): string {
        // Check if client has a direct type identifier
        if ("type" in client) {
            return (client as any).type;
        }

        // Check constructor name
        const constructorName = client.constructor?.name;
        if (constructorName && !constructorName.includes("Object")) {
            return constructorName.toLowerCase().replace("client", "");
        }

        // Fallback: Generate a unique identifier
        return `client_${Date.now()}`;
    }

    if (character.plugins?.length > 0) {
        for (const plugin of character.plugins) {
            if (plugin.clients) {
                for (const client of plugin.clients) {
                    const startedClient = await client.start(runtime);
                    const clientType = determineClientType(client);
                    elizaLogger.debug(
                        `Initializing client of type: ${clientType}`
                    );
                    clients[clientType] = startedClient;
                }
            }
        }
    }

    return {
        twitterFailed,
        clients,
    };
}

function getSecret(character: Character, secret: string) {
    return character.settings?.secrets?.[secret] || process.env[secret];
}

let nodePlugin: any | undefined;

export async function createAgent(
    character: Character,
    db: IDatabaseAdapter,
    cache: ICacheManager,
    token: string
): Promise<AgentRuntime> {
    elizaLogger.success(
        elizaLogger.successesTitle,
        "Creating runtime for character",
        character.name
    );

    nodePlugin ??= createNodePlugin();

    const teeMode = getSecret(character, "TEE_MODE") || "OFF";
    const walletSecretSalt = getSecret(character, "WALLET_SECRET_SALT");

    // Validate TEE configuration
    if (teeMode !== TEEMode.OFF && !walletSecretSalt) {
        elizaLogger.error(
            "WALLET_SECRET_SALT required when TEE_MODE is enabled"
        );
        throw new Error("Invalid TEE configuration");
    }

    let goatPlugin: any | undefined;
    if (getSecret(character, "EVM_PROVIDER_URL")) {
        goatPlugin = await createGoatPlugin((secret) =>
            getSecret(character, secret)
        );
    }

    return new AgentRuntime({
        databaseAdapter: db,
        token,
        modelProvider: character.modelProvider,
        evaluators: [],
        character,
        // character.plugins are handled when clients are added
        plugins: [
            bootstrapPlugin,
            getSecret(character, "CONFLUX_CORE_PRIVATE_KEY")
                ? confluxPlugin
                : null,
            nodePlugin,
            getSecret(character, "SOLANA_PUBLIC_KEY") ||
            (getSecret(character, "WALLET_PUBLIC_KEY") &&
                !getSecret(character, "WALLET_PUBLIC_KEY")?.startsWith("0x"))
                ? solanaPlugin
                : null,
            (getSecret(character, "NEAR_ADDRESS") ||
                getSecret(character, "NEAR_WALLET_PUBLIC_KEY")) &&
            getSecret(character, "NEAR_WALLET_SECRET_KEY")
                ? nearPlugin
                : null,
            getSecret(character, "EVM_PUBLIC_KEY") ||
            (getSecret(character, "WALLET_PUBLIC_KEY") &&
                getSecret(character, "WALLET_PUBLIC_KEY")?.startsWith("0x"))
                ? evmPlugin
                : null,
            (getSecret(character, "SOLANA_PUBLIC_KEY") ||
                (getSecret(character, "WALLET_PUBLIC_KEY") &&
                    !getSecret(character, "WALLET_PUBLIC_KEY")?.startsWith(
                        "0x"
                    ))) &&
            getSecret(character, "SOLANA_ADMIN_PUBLIC_KEY") &&
            getSecret(character, "SOLANA_PRIVATE_KEY") &&
            getSecret(character, "SOLANA_ADMIN_PRIVATE_KEY")
                ? nftGenerationPlugin
                : null,
            getSecret(character, "ZEROG_PRIVATE_KEY") ? zgPlugin : null,
            getSecret(character, "COINBASE_COMMERCE_KEY")
                ? coinbaseCommercePlugin
                : null,
            getSecret(character, "FAL_API_KEY") ||
            getSecret(character, "OPENAI_API_KEY") ||
            getSecret(character, "VENICE_API_KEY") ||
            getSecret(character, "HEURIST_API_KEY") ||
            getSecret(character, "LIVEPEER_GATEWAY_URL")
                ? imageGenerationPlugin
                : null,
            getSecret(character, "FAL_API_KEY") ? ThreeDGenerationPlugin : null,
            ...(getSecret(character, "COINBASE_API_KEY") &&
            getSecret(character, "COINBASE_PRIVATE_KEY")
                ? [
                      coinbaseMassPaymentsPlugin,
                      tradePlugin,
                      tokenContractPlugin,
                      advancedTradePlugin,
                  ]
                : []),
            ...(teeMode !== TEEMode.OFF && walletSecretSalt
                ? [teePlugin, solanaPlugin]
                : []),
            getSecret(character, "COINBASE_API_KEY") &&
            getSecret(character, "COINBASE_PRIVATE_KEY") &&
            getSecret(character, "COINBASE_NOTIFICATION_URI")
                ? webhookPlugin
                : null,
            getSecret(character, "EVM_PROVIDER_URL") ? goatPlugin : null,
            getSecret(character, "ABSTRACT_PRIVATE_KEY")
                ? abstractPlugin
                : null,
            getSecret(character, "FLOW_ADDRESS") &&
            getSecret(character, "FLOW_PRIVATE_KEY")
                ? flowPlugin
                : null,
            getSecret(character, "APTOS_PRIVATE_KEY") ? aptosPlugin : null,
            getSecret(character, "MVX_PRIVATE_KEY") ? multiversxPlugin : null,
            getSecret(character, "ZKSYNC_PRIVATE_KEY") ? zksyncEraPlugin : null,
            getSecret(character, "CRONOSZKEVM_PRIVATE_KEY")
                ? cronosZkEVMPlugin
                : null,
            getSecret(character, "TON_PRIVATE_KEY") ? tonPlugin : null,
            getSecret(character, "SUI_PRIVATE_KEY") ? suiPlugin : null,
            getSecret(character, "STORY_PRIVATE_KEY") ? storyPlugin : null,
            getSecret(character, "FUEL_PRIVATE_KEY") ? fuelPlugin : null,
            getSecret(character, "AVALANCHE_PRIVATE_KEY")
                ? avalanchePlugin
                : null,
        ].filter(Boolean),
        providers: [],
        actions: [],
        services: [],
        managers: [],
        cacheManager: cache,
        fetch: logFetch,
    });
}

function initializeFsCache(baseDir: string, character: Character) {
    const cacheDir = path.resolve(baseDir, character.id, "cache");

    const cache = new CacheManager(new FsCacheAdapter(cacheDir));
    return cache;
}

function initializeDbCache(character: Character, db: IDatabaseCacheAdapter) {
    const cache = new CacheManager(new DbCacheAdapter(db, character.id));
    return cache;
}

function initializeCache(
    cacheStore: string,
    character: Character,
    baseDir?: string,
    db?: IDatabaseCacheAdapter
) {
    switch (cacheStore) {
        case CacheStore.REDIS:
            if (process.env.REDIS_URL) {
                elizaLogger.info("Connecting to Redis...");
                const redisClient = new RedisClient(process.env.REDIS_URL);
                return new CacheManager(
                    new DbCacheAdapter(redisClient, character.id) // Using DbCacheAdapter since RedisClient also implements IDatabaseCacheAdapter
                );
            } else {
                throw new Error("REDIS_URL environment variable is not set.");
            }

        case CacheStore.DATABASE:
            if (db) {
                elizaLogger.info("Using Database Cache...");
                return initializeDbCache(character, db);
            } else {
                throw new Error(
                    "Database adapter is not provided for CacheStore.Database."
                );
            }

        case CacheStore.FILESYSTEM:
            elizaLogger.info("Using File System Cache...");
            return initializeFsCache(baseDir, character);

        default:
            throw new Error(
                `Invalid cache store: ${cacheStore} or required configuration missing.`
            );
    }
}

async function startTwitterAgent(
    character: Character,
    directClient: DirectClient
): Promise<AgentRuntime> {
    let db: IDatabaseAdapter & IDatabaseCacheAdapter;
    try {
        character.id ??= stringToUuid(character.name);
        character.username ??= character.name;
        const dataDir = path.join(__dirname, "../data");

        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }

        db = initializeDatabase(dataDir) as IDatabaseAdapter &
            IDatabaseCacheAdapter;

        await db.init();
        const token = getTokenForProvider(character.modelProvider, character);

        const cache = initializeCache(
            process.env.CACHE_STORE ?? CacheStore.DATABASE,
            character,
            "",
            db
        ); // "" should be replaced with dir for file system caching. THOUGHTS: might probably make this into an env

        const runtime: AgentRuntime = await createAgent(
            character,
            db,
            cache,
            token
        );
        await runtime.initialize();
        const rooms = await db.getRooms();
        const room = rooms.find((r: any) => r.id === runtime.agentId);
        let roomCharacter = character;

        if (room && room.character) {
            if (typeof room.character === "string") {
                try {
                    roomCharacter = JSON.parse(room.character);
                } catch (error) {
                    elizaLogger.error("Failed to parse room character:", error);
                }
            } else {
                roomCharacter = room.character;
            }
        }

        let roomSettings: any = {};
        if (room && room.settings) {
            if (typeof room.settings === "string") {
                try {
                    roomSettings = JSON.parse(room.settings);
                } catch (error) {
                    elizaLogger.error("Failed to parse room settings:", error);
                }
            } else {
                roomSettings = room.settings;
            }
        }

        const defaultSettings = {
            followProfiles: false,
            processionActions: false,
            schedulingPosts: false,
            postInterval: 0,
            twitterTargetUsers: "",
            actionInterval: 0,
            followInterval: 0,
        };
        let accountSettings = {};
        if (room && room.status === "active") {
            accountSettings = {
                ...defaultSettings,
                ...(roomCharacter.settings?.secrets || {}),
            };
        }
        const { clients, twitterFailed } = await initializeClients(
            character,
            runtime,
            accountSettings,
            false,
            roomSettings
        );
        if (!twitterFailed) {
            runtime.clients = clients as any;
        } else {
            elizaLogger.error(
                `Error initializing clients for ${character.name}:`
            );
            await db.updateRoomStatus(character.id, "stopped");
        }
        directClient.registerAgent(runtime);

        // report to console
        elizaLogger.debug(`Started ${character.name} as ${runtime.agentId}`);
        elizaLogger.debug("Clients:", runtime);
        return runtime;
    } catch (error) {
        if (db && character.id) {
            await db.updateRoomStatus(character.id, "stopped");
        }
        elizaLogger.error(
            `Error starting agent for character ${character.name}:`,
            error
        );
        elizaLogger.error(error);
        if (db) {
            await db.close();
        }
        throw error;
    }
}

async function startLinkedInAgent(
    character: Character,
    directClient: DirectClient,
    start = false
): Promise<AgentRuntime> {
    let db: IDatabaseAdapter & IDatabaseCacheAdapter;
    try {
        character.id ??= stringToUuid(character.name);
        character.username ??= character.name;
        const dataDir = path.join(__dirname, "../data");

        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }

        db = initializeDatabase(dataDir) as IDatabaseAdapter &
            IDatabaseCacheAdapter;

        await db.init();
        const token = getTokenForProvider(character.modelProvider, character);

        const cache = initializeCache(
            process.env.CACHE_STORE ?? CacheStore.DATABASE,
            character,
            "",
            db
        ); // "" should be replaced with dir for file system caching. THOUGHTS: might probably make this into an env

        const runtime: AgentRuntime = await createAgent(
            character,
            db,
            cache,
            token
        );
        await runtime.initialize();
        const rooms = await db.getRooms();

        const room = rooms.find((r: any) => r.id === runtime.agentId);
        let roomCharacter = character;

        // Parse room character if it exists
        if (room && room.character) {
            if (typeof room.character === "string") {
                try {
                    roomCharacter = JSON.parse(room.settings);
                } catch (error) {
                    elizaLogger.error("Failed to parse room character:", error);
                }
            } else {
                roomCharacter = room.character;
            }
        }

        let roomSettings: any = {};
        if (room && room.settings) {
            if (typeof room.settings === "string") {
                try {
                    roomSettings = JSON.parse(room.settings);
                    elizaLogger.debug("Parsed room settings:", roomSettings);
                } catch (error) {
                    elizaLogger.error("Failed to parse room settings:", error);
                }
            } else {
                elizaLogger.debug("Room settings object:", room);
                roomSettings = room.settings;
            }
        }
        elizaLogger.debug("Using room settings:", roomSettings);

        let accountSettings = {};
        if (room && room.status === "active") {
            accountSettings = {
                schedulingPosts: roomSettings.schedulingPosts || false,
                postInterval: roomSettings.postInterval || 480,
                userOrganization: roomSettings.userOrganization || null,
                ...(roomCharacter.settings?.secrets || {}),
            };
        }

        const { clients } = await initializeClients(
            character,
            runtime,
            accountSettings,
            start,
            roomSettings
        );
        runtime.clients = clients as any;

        if (runtime.clients && runtime.clients.linkedin) {
            const linkedinClient = runtime.clients.linkedin;
            let userOrganizations = null;

            // Fetch first organization after successful login
            try {
                elizaLogger.log(
                    "Fetching first LinkedIn organization for user:",
                    linkedinClient.client.profile.username
                );

                // Get user organizations to find the first one
                userOrganizations = await linkedinClient.getUserOrganizations();

                if (userOrganizations.organizations.length > 0) {
                    const firstOrg = userOrganizations.organizations[0];
                    console.log("=== First LinkedIn Organization ===");
                    console.log(`Organization ID: ${firstOrg.id}`);
                    console.log(
                        `Localized Name: ${firstOrg.localizedName || firstOrg.name}`
                    );
                    console.log("=== End Organization Info ===\n");
                } else {
                    console.log("No organizations found for this user.");
                }
            } catch (orgError) {
                // Don't let organization fetching errors break the main flow
                elizaLogger.warn(
                    "Could not fetch LinkedIn organizations (this is optional):",
                    orgError instanceof Error
                        ? orgError.message
                        : String(orgError)
                );
                console.log("⚠️  LinkedIn organization features are disabled.");
            }
        }

        directClient.registerAgent(runtime);

        // report to console
        elizaLogger.debug(`Started ${character.name} as ${runtime.agentId}`);

        return runtime;
    } catch (error) {
        if (db && character.id) {
            await db.updateRoomStatus(character.id, "stopped");
        }
        elizaLogger.error(
            `Error starting agent for character ${character.name}:`,
            error
        );
        elizaLogger.error(error);
        if (db) {
            await db.close();
        }
        throw error;
    }
}

const decryptPassword = (encryptedPassword: string) => {
    try {
        const publicKey = process.env.PASSWORD_PUBLIC_KEY || "";
        const encrypted = forge.util.decode64(encryptedPassword);
        const privateKey = forge.pki.privateKeyFromPem(publicKey);
        const decrypted = privateKey.decrypt(encrypted, "RSA-OAEP", {
            md: forge.md.sha256.create(),
            mgf1: {
                md: forge.md.sha256.create(),
            },
        });

        return decrypted;
    } catch (error) {
        console.error("Decryption failed:", error);
        throw new Error("Failed to decrypt password");
    }
};

const startAgents = async () => {
    const directClient = new DirectClient();
    const serverPort = 2151;
    let db: IDatabaseAdapter & IDatabaseCacheAdapter;
    const dataDir = path.join(__dirname, "../data");

    if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
    }

    try {
        // Initialize database with error handling
        db = initializeDatabase(dataDir) as IDatabaseAdapter &
            IDatabaseCacheAdapter;

        try {
            // Initialize database but don't let it crash the application
            await db.init();
            elizaLogger.info("Database initialization completed");
        } catch (dbInitError) {
            elizaLogger.error("Database initialization error:", dbInitError);
            elizaLogger.warn("Continuing with limited database functionality");
        }

        let rooms = [];
        try {
            // Get rooms but handle potential errors
            rooms = await db.getRooms();
            elizaLogger.info(`Found ${rooms.length} rooms`);
        } catch (roomsError) {
            elizaLogger.error("Error fetching rooms:", roomsError);
            elizaLogger.warn("Continuing without room data");
        }

        // Process rooms if we have any
        for (const room of rooms) {
            if (room.status === "active" && room.character) {
                try {
                    let savedCharacter;
                    if (typeof room.character === "string") {
                        try {
                            savedCharacter = JSON.parse(room.character);
                        } catch (error) {
                            elizaLogger.error(
                                "Failed to parse room character:",
                                error
                            );
                        }
                    } else {
                        savedCharacter = room.character;
                    }
                    if (savedCharacter.clients?.includes(Clients.LINKEDIN)) {
                        try {
                            const runtime = await startLinkedInAgent(
                                savedCharacter,
                                directClient,
                                true
                            );
                            directClient.registerAgent(runtime);
                            await new Promise((resolve) =>
                                setTimeout(resolve, 10000)
                            );
                        } catch (linkedinError) {
                            elizaLogger.error(
                                `Error starting LinkedIn agent for room ${room.id}:`,
                                linkedinError
                            );
                        }
                    }
                    if (savedCharacter.clients?.includes(Clients.TWITTER)) {
                        try {
                            const runtime = await startTwitterAgent(
                                savedCharacter,
                                directClient
                            );
                            directClient.registerAgent(runtime);
                            await new Promise((resolve) =>
                                setTimeout(resolve, 10000)
                            );
                        } catch (twitterError) {
                            elizaLogger.error(
                                `Error starting Twitter agent for room ${room.id}:`,
                                twitterError
                            );
                        }
                    }
                } catch (parseError) {
                    elizaLogger.error(
                        `Error parsing character for room ${room.id}:`,
                        parseError
                    );
                }
            }
        }
    } catch (error) {
        elizaLogger.error("Critical error in startAgents:", error);
        // Continue with a basic direct client even if database fails completely
        elizaLogger.warn("Starting with limited functionality (no database)");
    }

    // upload some agent functionality into directClient
    directClient.startTwitterAgent = async (character: Character) => {
        const res = await startTwitterAgent(character, directClient);
        return res;
    };
    directClient.startLinkedInAgent = async (character: Character) => {
        const res = await startLinkedInAgent(character, directClient);
        return res;
    };
    directClient.db = db;

    directClient.start(serverPort);

    if (serverPort !== parseInt(settings.SERVER_PORT || "3000")) {
        elizaLogger.log(`Server started on alternate port ${serverPort}`);
    }

    elizaLogger.log(
        "Run `pnpm start:client` to start the client and visit the outputted URL (http://localhost:5173) to chat with your agents. When running multiple agents, use client with different port `SERVER_PORT=3001 pnpm start:client`"
    );
};

startAgents().catch((error) => {
    elizaLogger.error("Unhandled error in startAgents:", error);
    process.exit(1);
});
